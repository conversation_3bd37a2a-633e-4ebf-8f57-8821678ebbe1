#!/usr/bin/env node

/**
 * Service Worker Toggle Script
 * Enables or disables service worker registration
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');
const mainTsxPath = path.join(projectRoot, 'src', 'main.tsx');

const action = process.argv[2];

if (!action || !['enable', 'disable'].includes(action)) {
  console.log('Usage: node scripts/toggle-service-worker.js [enable|disable]');
  process.exit(1);
}

try {
  let content = fs.readFileSync(mainTsxPath, 'utf8');

  if (action === 'enable') {
    // Enable service worker registration
    content = content.replace(
      /\/\/ serviceWorkerRegistration\.register\(/g,
      'serviceWorkerRegistration.register('
    );
    content = content.replace(
      /serviceWorkerRegistration\.unregister\(\);/g,
      '// serviceWorkerRegistration.unregister();'
    );
    console.log('✅ Service worker registration enabled');
    console.log('⚠️  Make sure service-worker.js is properly built and deployed');
  } else {
    // Disable service worker registration
    content = content.replace(
      /serviceWorkerRegistration\.register\(/g,
      '// serviceWorkerRegistration.register('
    );
    content = content.replace(
      /\/\/ serviceWorkerRegistration\.unregister\(\);/g,
      'serviceWorkerRegistration.unregister();'
    );
    console.log('✅ Service worker registration disabled');
    console.log('ℹ️  This prevents 404 errors in production');
  }

  fs.writeFileSync(mainTsxPath, content);
  console.log('📝 Updated src/main.tsx');
  console.log('🔄 Rebuild the app for changes to take effect');

} catch (error) {
  console.error('❌ Error updating service worker configuration:', error.message);
  process.exit(1);
}
