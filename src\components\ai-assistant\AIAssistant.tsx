import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/context/AuthContext';
import { useSchool } from '@/context/SchoolContext';
import { aiAssistantService, ChatMessage, UserContext } from '@/lib/services/ai-assistant-service';
import ChatBubble from './ChatBubble';
import ChatWindow from './ChatWindow';
import GreetingNotification from './GreetingNotification';
import { useGreetingNotification } from '@/hooks/useGreetingNotification';
import { toast } from 'sonner';

export default function AIAssistant() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [hasUnreadMessages, setHasUnreadMessages] = useState(false);
  const [showWelcomeMessage, setShowWelcomeMessage] = useState(false);

  const { user } = useAuth();
  const { currentSchool } = useSchool();
  const { i18n, t } = useTranslation();
  const { showGreeting, greetingType, userRole, hideGreeting } = useGreetingNotification();

  // Initialize user context and language when user, school, or language changes
  useEffect(() => {
    if (user && currentSchool) {
      const userContext: UserContext = {
        userId: user.id,
        userName: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
        userRole: user.user_metadata?.role || 'student',
        schoolId: currentSchool.id,
        schoolName: currentSchool.name,
        currentPage: window.location.pathname
      };

      aiAssistantService.setUserContext(userContext);
    }

    // Set language for AI responses
    aiAssistantService.setLanguage(i18n.language || 'en');

    // Set translation function for action labels
    aiAssistantService.setTranslateFunction(t);
  }, [user, currentSchool, i18n.language, t]);

  // Load conversation history on mount
  useEffect(() => {
    const history = aiAssistantService.getConversationHistory();
    setMessages(history);
  }, []);

  // Check for first-time user and show welcome message
  useEffect(() => {
    const hasSeenWelcome = localStorage.getItem('ai-assistant-welcome-shown');
    if (!hasSeenWelcome) {
      // Show welcome message after a short delay
      const timer = setTimeout(() => {
        setShowWelcomeMessage(true);
        localStorage.setItem('ai-assistant-welcome-shown', 'true');

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
          setShowWelcomeMessage(false);
        }, 5000);
      }, 1000); // Show after 1 second of page load

      return () => clearTimeout(timer);
    }
  }, []);

  // Handle chat bubble click
  const handleChatToggle = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setHasUnreadMessages(false);
    }
    // Dismiss welcome message when chat is opened
    if (showWelcomeMessage) {
      setShowWelcomeMessage(false);
    }
  };

  // Handle welcome message dismissal
  const handleWelcomeMessageDismiss = () => {
    setShowWelcomeMessage(false);
  };

  // Type message character by character
  const typeMessage = async (fullContent: string, messageId: string) => {
    let currentContent = '';

    for (let i = 0; i < fullContent.length; i++) {
      currentContent += fullContent[i];

      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? { ...msg, content: currentContent }
          : msg
      ));

      // Variable delay for more natural typing
      let delay = 30; // Base delay

      // Longer pause after punctuation
      if (['.', '!', '?', '\n'].includes(fullContent[i])) {
        delay = 200;
      } else if ([',', ';', ':'].includes(fullContent[i])) {
        delay = 100;
      } else if (fullContent[i] === ' ') {
        delay = 50;
      }

      // Add some randomness for natural feel
      delay += Math.random() * 20;

      await new Promise(resolve => setTimeout(resolve, delay));
    }

    // Mark typing as complete
    setMessages(prev => prev.map(msg =>
      msg.id === messageId
        ? { ...msg, isTyping: false }
        : msg
    ));
  };

  // Handle sending a message
  const handleSendMessage = async (messageContent: string) => {
    try {
      setIsTyping(true);

      // Add user message immediately
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'user',
        content: messageContent,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, userMessage]);

      // Show thinking animation for 2 seconds
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Get AI response
      const botResponse = await aiAssistantService.sendMessage(messageContent);

      // Stop typing indicator
      setIsTyping(false);

      // Add bot response with empty content initially
      const typingBotMessage: ChatMessage = {
        ...botResponse,
        content: '',
        isTyping: true
      };

      setMessages(prev => [...prev, typingBotMessage]);

      // Type out the response character by character
      await typeMessage(botResponse.content, botResponse.id);

      // Show unread indicator if chat is closed
      if (!isOpen) {
        setHasUnreadMessages(true);
      }

    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message. Please try again.');

      // Add error message
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'bot',
        content: 'Sorry, I encountered an error. Please try again or contact support if the problem persists.',
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMessage]);
      setIsTyping(false);
    }
  };

  // Handle closing chat
  const handleClose = () => {
    setIsOpen(false);
  };

  // Don't render if user is not authenticated
  if (!user) {
    return null;
  }

  return (
    <>
      {/* Greeting Notification */}
      <GreetingNotification
        isVisible={showGreeting}
        onClose={hideGreeting}
        greetingType={greetingType}
        userRole={userRole}
      />

      {/* Chat Bubble */}
      <ChatBubble
        onClick={handleChatToggle}
        isOpen={isOpen}
        hasUnreadMessages={hasUnreadMessages}
        isTyping={isTyping}
        showWelcomeMessage={showWelcomeMessage}
        onWelcomeMessageDismiss={handleWelcomeMessageDismiss}
      />

      {/* Chat Window */}
      <ChatWindow
        isOpen={isOpen}
        messages={messages}
        onSendMessage={handleSendMessage}
        isTyping={isTyping}
        onClose={handleClose}
      />
    </>
  );
}
