@echo off
REM 🚀 Automated Vercel Deployment Script for Windows
REM This script prepares and deploys the attendance tracking system to Vercel

echo 🚀 Starting Vercel Deployment Process...
echo ========================================

REM Check if we're in the right directory
if not exist "package.json" (
    echo [ERROR] package.json not found. Please run this script from the project root.
    pause
    exit /b 1
)

REM Check if git is initialized
if not exist ".git" (
    echo [ERROR] Git repository not initialized. Please run 'git init' first.
    pause
    exit /b 1
)

REM Check for uncommitted changes and commit them
echo [INFO] Checking git status...
git status --porcelain > temp_git_status.txt
for /f %%i in ("temp_git_status.txt") do set size=%%~zi
del temp_git_status.txt

if %size% gtr 0 (
    echo [WARNING] You have uncommitted changes. Committing them now...
    git add .
    git commit -m "feat: prepare for production deployment"
    echo [SUCCESS] Changes committed successfully
)

REM Install dependencies
echo [INFO] Installing dependencies...
where bun >nul 2>nul
if %errorlevel% equ 0 (
    bun install
) else (
    where npm >nul 2>nul
    if %errorlevel% equ 0 (
        npm install
    ) else (
        echo [ERROR] Neither npm nor bun found. Please install Node.js.
        pause
        exit /b 1
    )
)

REM Run build to ensure everything works
echo [INFO] Running production build test...
where bun >nul 2>nul
if %errorlevel% equ 0 (
    bun run build
) else (
    npm run build
)

if %errorlevel% neq 0 (
    echo [ERROR] Build failed. Please fix the errors and try again.
    pause
    exit /b 1
)
echo [SUCCESS] Build completed successfully

REM Check if Vercel CLI is installed
where vercel >nul 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] Vercel CLI not found. Installing...
    npm install -g vercel
    echo [SUCCESS] Vercel CLI installed
)

REM Check Vercel authentication
echo [INFO] Checking Vercel authentication...
vercel whoami >nul 2>nul
if %errorlevel% neq 0 (
    echo [INFO] Please log in to Vercel...
    vercel login
)

REM Deploy to Vercel
echo [INFO] Deploying to Vercel...
vercel --prod

if %errorlevel% equ 0 (
    echo.
    echo [SUCCESS] Deployment completed!
    echo.
    echo 🎉 Your attendance tracking system is now live!
    echo.
    echo 📋 Next Steps:
    echo 1. Configure your custom domain in Vercel dashboard
    echo 2. Set up production environment variables
    echo 3. Test all functionality on the live site
    echo 4. Create user accounts for your pilot schools
    echo.
    echo 📚 Documentation:
    echo - Deployment Guide: ./DEPLOYMENT_GUIDE.md
    echo - User Guides: ./docs/user-guides/
    echo.
    echo 🔗 Useful Links:
    echo - Vercel Dashboard: https://vercel.com/dashboard
    echo - Supabase Dashboard: https://supabase.com/dashboard
    echo.
    echo [SUCCESS] Deployment script completed successfully!
) else (
    echo [ERROR] Deployment failed. Please check the error messages above.
)

echo.
echo Press any key to continue...
pause >nul
