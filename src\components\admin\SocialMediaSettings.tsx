import { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import { useTranslation } from "react-i18next";
import { useAuth } from "@/context/AuthContext";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import {
  Instagram,
  Twitter,
  Facebook,
  Youtube,
  Globe,
  Save,
  Eye,
  EyeOff,
  Sparkles,
  Settings,
  RefreshCw,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import SocialMediaGuide from "./SocialMediaGuide";

interface SocialMediaSettings {
  enabled: boolean;
  platforms: {
    instagram?: {
      enabled: boolean;
      username: string;
      embed_code?: string;
    };
    twitter?: {
      enabled: boolean;
      username: string;
      embed_code?: string;
    };
    facebook?: {
      enabled: boolean;
      page_url: string;
      embed_code?: string;
    };
    youtube?: {
      enabled: boolean;
      channel_url: string;
      channel_id?: string;
      embed_code?: string;
    };
    website?: {
      enabled: boolean;
      url: string;
      rss_feed?: string;
    };
  };
  refresh_interval_minutes: number;
  show_engagement_stats: boolean;
}

export default function SocialMediaSettings() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { profile } = useAuth();
  const [settings, setSettings] = useState<SocialMediaSettings>({
    enabled: false,
    platforms: {
      instagram: { enabled: false, username: "", embed_code: "" },
      twitter: { enabled: false, username: "", embed_code: "" },
      facebook: { enabled: false, page_url: "", embed_code: "" },
      youtube: { enabled: false, channel_url: "", channel_id: "", embed_code: "" },
      website: { enabled: false, url: "", rss_feed: "" },
    },
    refresh_interval_minutes: 30,
    show_engagement_stats: true,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showEmbedCodes, setShowEmbedCodes] = useState(false);
  const [activeTab, setActiveTab] = useState("settings");

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    if (!profile?.school_id) return;

    try {
      const { data, error } = await supabase
        .from("school_settings")
        .select("social_media_integration")
        .eq("school_id", profile.school_id)
        .single();

      if (error && error.code !== "PGRST116") {
        throw error;
      }

      if (data?.social_media_integration) {
        setSettings(data.social_media_integration as SocialMediaSettings);
      }
    } catch (error) {
      console.error("Error fetching social media settings:", error);
      toast({
        title: t("common.error"),
        description: t("admin.socialMedia.failedToLoadSettings"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    if (!profile?.school_id) return;

    setSaving(true);
    try {
      // First check if settings exist for this school
      const { data: existingSettings, error: checkError } = await supabase
        .from("school_settings")
        .select("id")
        .eq("school_id", profile.school_id)
        .single();

      if (checkError && checkError.code !== "PGRST116") {
        throw checkError;
      }

      if (existingSettings) {
        // Update existing settings
        const { error } = await supabase
          .from("school_settings")
          .update({
            social_media_integration: settings,
            updated_at: new Date().toISOString(),
          })
          .eq("school_id", profile.school_id);

        if (error) throw error;
      } else {
        // Insert new settings
        const { error } = await supabase
          .from("school_settings")
          .insert({
            school_id: profile.school_id,
            social_media_integration: settings,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });

        if (error) throw error;
      }

      toast({
        title: t("admin.settings.settingsSaved"),
        description: t("admin.socialMedia.settingsUpdatedSuccessfully"),
      });
    } catch (error) {
      console.error("Error saving social media settings:", error);
      toast({
        title: t("common.error"),
        description: t("admin.socialMedia.failedToSaveSettings"),
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const updatePlatformSetting = (platform: string, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      platforms: {
        ...prev.platforms,
        [platform]: {
          ...prev.platforms[platform as keyof typeof prev.platforms],
          [field]: value,
        },
      },
    }));
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case "instagram":
        return <Instagram className="w-5 h-5" />;
      case "twitter":
        return <Twitter className="w-5 h-5" />;
      case "facebook":
        return <Facebook className="w-5 h-5" />;
      case "youtube":
        return <Youtube className="w-5 h-5" />;
      case "website":
        return <Globe className="w-5 h-5" />;
      default:
        return <Globe className="w-5 h-5" />;
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case "instagram":
        return "text-pink-500";
      case "twitter":
        return "text-blue-500";
      case "facebook":
        return "text-blue-600";
      case "youtube":
        return "text-red-500";
      case "website":
        return "text-gray-500";
      default:
        return "text-gray-500";
    }
  };

  const getEmbedPlaceholder = (platform: string) => {
    switch (platform) {
      case "facebook":
        return t("admin.socialMedia.embedPlaceholders.facebook");
      case "twitter":
        return t("admin.socialMedia.embedPlaceholders.twitter");
      case "instagram":
        return t("admin.socialMedia.embedPlaceholders.instagram");
      case "youtube":
        return t("admin.socialMedia.embedPlaceholders.youtube");
      default:
        return t("admin.socialMedia.embedPlaceholders.default");
    }
  };

  const getEmbedInstructions = (platform: string) => {
    switch (platform) {
      case "facebook":
        return (
          <>
            <p>1. {t("admin.socialMedia.embedInstructions.facebook.step1")} <a href="https://developers.facebook.com/docs/plugins/page-plugin" target="_blank" rel="noopener noreferrer" className="text-blue-500 underline">Facebook Page Plugin</a></p>
            <p>2. {t("admin.socialMedia.embedInstructions.facebook.step2")}</p>
            <p>3. {t("admin.socialMedia.embedInstructions.facebook.step3")}</p>
            <p>4. {t("admin.socialMedia.embedInstructions.facebook.step4")}</p>
          </>
        );
      case "twitter":
        return (
          <>
            <p>1. {t("admin.socialMedia.embedInstructions.twitter.step1")} <a href="https://publish.twitter.com/" target="_blank" rel="noopener noreferrer" className="text-blue-500 underline">Twitter Publish</a></p>
            <p>2. {t("admin.socialMedia.embedInstructions.twitter.step2")}</p>
            <p>3. {t("admin.socialMedia.embedInstructions.twitter.step3")}</p>
            <p>4. {t("admin.socialMedia.embedInstructions.twitter.step4")}</p>
          </>
        );
      case "instagram":
        return (
          <>
            <p>1. {t("admin.socialMedia.embedInstructions.instagram.step1")}</p>
            <p>2. {t("admin.socialMedia.embedInstructions.instagram.step2")}</p>
            <p>3. {t("admin.socialMedia.embedInstructions.instagram.step3")}</p>
            <p>4. {t("admin.socialMedia.embedInstructions.instagram.step4")}</p>
            <p><strong>{t("admin.socialMedia.embedInstructions.instagram.note")}</strong></p>
          </>
        );
      case "youtube":
        return (
          <div className="space-y-1">
            <p>✨ <strong>{t("admin.socialMedia.embedInstructions.youtube.title")}</strong></p>
            <p>1. {t("admin.socialMedia.embedInstructions.youtube.step1")}</p>
            <p>2. {t("admin.socialMedia.embedInstructions.youtube.step2")}</p>
            <p>3. {t("admin.socialMedia.embedInstructions.youtube.step3")}</p>
            <p>4. {t("admin.socialMedia.embedInstructions.youtube.step4")}</p>
            <p><em>{t("admin.socialMedia.embedInstructions.youtube.note")}</em></p>
          </div>
        );
      default:
        return <p>{t("admin.socialMedia.embedInstructions.default")}</p>;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Sparkles className="w-5 h-5" />
            <span>Social Media Integration</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
          <div className="flex items-center space-x-2">
            <Sparkles className="w-5 h-5 flex-shrink-0" />
            <span className="text-base sm:text-lg">{t("admin.socialMedia.title")}</span>
          </div>
          <Badge variant={settings.enabled ? "default" : "secondary"} className="self-start sm:self-center">
            {settings.enabled ? t("admin.socialMedia.enabled") : t("admin.socialMedia.disabled")}
          </Badge>
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          {t("admin.socialMedia.description")}
        </p>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 h-auto">
            <TabsTrigger value="settings" className="text-xs sm:text-sm py-2 px-2 sm:px-4">
              {t("admin.socialMedia.settings")}
            </TabsTrigger>
            <TabsTrigger value="guide" className="text-xs sm:text-sm py-2 px-2 sm:px-4">
              {t("admin.socialMedia.setupGuide")}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="settings" className="space-y-6 mt-6">
            {/* Master Enable/Disable */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 p-4 border rounded-lg">
          <div className="flex-1">
            <Label className="text-sm sm:text-base font-medium">{t("admin.socialMedia.enableFeed")}</Label>
            <p className="text-xs sm:text-sm text-muted-foreground mt-1">
              {t("admin.socialMedia.enableFeedDescription")}
            </p>
          </div>
          <Switch
            checked={settings.enabled}
            onCheckedChange={(checked) =>
              setSettings(prev => ({ ...prev, enabled: checked }))
            }
            className="self-start sm:self-center"
          />
        </div>

        {settings.enabled && (
          <>
            {/* Global Settings */}
            <div className="space-y-4">
              <h3 className="text-base sm:text-lg font-semibold flex items-center space-x-2">
                <Settings className="w-4 h-4 flex-shrink-0" />
                <span>{t("admin.socialMedia.globalSettings")}</span>
              </h3>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="refresh-interval" className="text-sm font-medium">
                    {t("admin.socialMedia.refreshInterval")}
                  </Label>
                  <Input
                    id="refresh-interval"
                    type="number"
                    min="5"
                    max="1440"
                    value={settings.refresh_interval_minutes}
                    onChange={(e) =>
                      setSettings(prev => ({
                        ...prev,
                        refresh_interval_minutes: parseInt(e.target.value) || 30,
                      }))
                    }
                    className="text-sm"
                  />
                  <p className="text-xs text-muted-foreground">
                    {t("admin.socialMedia.refreshIntervalDescription")}
                  </p>
                </div>

                <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 p-3 border rounded-lg">
                  <Switch
                    id="show-engagement"
                    checked={settings.show_engagement_stats}
                    onCheckedChange={(checked) =>
                      setSettings(prev => ({ ...prev, show_engagement_stats: checked }))
                    }
                  />
                  <Label htmlFor="show-engagement" className="text-sm font-medium cursor-pointer">
                    {t("admin.socialMedia.showEngagementStats")}
                  </Label>
                </div>
              </div>
            </div>

            <Separator />

            {/* Platform Configuration */}
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                <h3 className="text-base sm:text-lg font-semibold">{t("admin.socialMedia.platformConfiguration")}</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowEmbedCodes(!showEmbedCodes)}
                  className="self-start sm:self-center text-xs sm:text-sm"
                >
                  {showEmbedCodes ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  <span className="ml-2 hidden sm:inline">
                    {showEmbedCodes ? t("admin.socialMedia.hideEmbedCodes") : t("admin.socialMedia.showEmbedCodes")}
                  </span>
                  <span className="ml-2 sm:hidden">
                    {showEmbedCodes ? "Hide" : "Show"}
                  </span>
                </Button>
              </div>

              <div className="grid gap-4 sm:gap-6">
                {Object.entries(settings.platforms).map(([platform, config]) => (
                  <div key={platform} className="border rounded-lg p-3 sm:p-4 space-y-4">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                      <div className="flex items-center space-x-3 flex-1">
                        <div className={cn("p-2 rounded-lg bg-muted flex-shrink-0", getPlatformColor(platform))}>
                          {getPlatformIcon(platform)}
                        </div>
                        <div className="min-w-0 flex-1">
                          <h4 className="font-medium capitalize text-sm sm:text-base">{platform}</h4>
                          <p className="text-xs sm:text-sm text-muted-foreground truncate">
                            {platform === "website" ? t("admin.socialMedia.schoolWebsite") : t("admin.socialMedia.platformIntegration", { platform })}
                          </p>
                        </div>
                      </div>
                      <Switch
                        checked={config.enabled}
                        onCheckedChange={(checked) =>
                          updatePlatformSetting(platform, "enabled", checked)
                        }
                        className="self-start sm:self-center"
                      />
                    </div>

                    {config.enabled && (
                      <div className="space-y-3 pl-0 sm:pl-11">
                        {platform === "instagram" && (
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">{t("admin.socialMedia.instagramUsername")}</Label>
                            <Input
                              placeholder={t("admin.socialMedia.usernamePlaceholder")}
                              value={config.username || ""}
                              onChange={(e) =>
                                updatePlatformSetting(platform, "username", e.target.value)
                              }
                              className="text-sm"
                            />
                          </div>
                        )}

                        {platform === "twitter" && (
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">{t("admin.socialMedia.twitterUsername")}</Label>
                            <Input
                              placeholder={t("admin.socialMedia.usernamePlaceholder")}
                              value={config.username || ""}
                              onChange={(e) =>
                                updatePlatformSetting(platform, "username", e.target.value)
                              }
                              className="text-sm"
                            />
                          </div>
                        )}

                        {platform === "facebook" && (
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">{t("admin.socialMedia.facebookPageUrl")}</Label>
                            <Input
                              placeholder="https://facebook.com/yourschool"
                              value={config.page_url || ""}
                              onChange={(e) =>
                                updatePlatformSetting(platform, "page_url", e.target.value)
                              }
                              className="text-sm"
                            />
                          </div>
                        )}

                        {platform === "youtube" && (
                          <div className="space-y-3">
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">{t("admin.socialMedia.youtubeChannelUrl")}</Label>
                              <Input
                                placeholder="https://youtube.com/@yourschool"
                                value={config.channel_url || ""}
                                onChange={(e) =>
                                  updatePlatformSetting(platform, "channel_url", e.target.value)
                                }
                                className="text-sm"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">{t("admin.socialMedia.youtubeChannelId")}</Label>
                              <Input
                                placeholder="UCxxxxxxxxxxxxxxxxxx (24 characters starting with UC)"
                                value={config.channel_id || ""}
                                onChange={(e) =>
                                  updatePlatformSetting(platform, "channel_id", e.target.value)
                                }
                                className="text-sm"
                              />
                              <div className="text-xs text-muted-foreground space-y-1 p-2 bg-muted/50 rounded-md">
                                <p><strong>✨ {t("admin.socialMedia.youtubeHelp.title")}</strong></p>
                                <p>• {t("admin.socialMedia.youtubeHelp.enterChannelId")}</p>
                                <p>• {t("admin.socialMedia.youtubeHelp.gridLayout")}</p>
                                <p>• {t("admin.socialMedia.youtubeHelp.individualVideos")}</p>
                                <p>• {t("admin.socialMedia.youtubeHelp.findChannelId")}</p>
                                <p><strong>{t("admin.socialMedia.youtubeHelp.perfect")}</strong></p>
                              </div>
                            </div>
                          </div>
                        )}

                        {platform === "website" && (
                          <>
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">{t("admin.socialMedia.websiteUrl")}</Label>
                              <Input
                                placeholder="https://yourschool.edu"
                                value={config.url || ""}
                                onChange={(e) =>
                                  updatePlatformSetting(platform, "url", e.target.value)
                                }
                                className="text-sm"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">{t("admin.socialMedia.rssFeedUrl")}</Label>
                              <Input
                                placeholder="https://yourschool.edu/feed"
                                value={config.rss_feed || ""}
                                onChange={(e) =>
                                  updatePlatformSetting(platform, "rss_feed", e.target.value)
                                }
                                className="text-sm"
                              />
                            </div>
                          </>
                        )}

                        {showEmbedCodes && (
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">{t("admin.socialMedia.embedCode")}</Label>
                            <Textarea
                              placeholder={getEmbedPlaceholder(platform)}
                              value={config.embed_code || ""}
                              onChange={(e) =>
                                updatePlatformSetting(platform, "embed_code", e.target.value)
                              }
                              rows={4}
                              className="text-xs sm:text-sm font-mono"
                            />
                            <div className="text-xs text-muted-foreground space-y-1 p-2 bg-muted/50 rounded-md">
                              <p><strong>{t("admin.socialMedia.toShowRealPosts")}</strong></p>
                              {getEmbedInstructions(platform)}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

            {/* Save Button */}
            <div className="flex justify-center sm:justify-end pt-4">
              <Button onClick={saveSettings} disabled={saving} className="w-full sm:w-auto">
                {saving ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                <span className="text-sm sm:text-base">{t("admin.settings.saveSettings")}</span>
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="guide" className="mt-6">
            <SocialMediaGuide />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
