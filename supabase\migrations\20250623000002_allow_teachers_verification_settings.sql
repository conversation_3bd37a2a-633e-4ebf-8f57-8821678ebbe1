-- Allow teachers to manage verification method settings in school_settings table
-- This migration adds a new RLS policy to allow teachers to configure verification settings

-- Add RLS policy for teachers to manage verification method settings
CREATE POLICY "Teachers can manage verification settings in their school"
ON public.school_settings
FOR ALL
TO authenticated
USING (
  -- Check if the current user is a teacher (no access_level requirement)
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Check if the settings belong to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  -- Check if the current user is a teacher (no access_level requirement)
  EXISTS (
    SELECT 1 FROM profiles teacher
    WHERE teacher.user_id = auth.uid()
    AND teacher.role = 'teacher'
  )
  AND
  -- Check if the settings belong to the same school
  school_id = (
    SELECT school_id FROM profiles
    WHERE user_id = auth.uid()
  )
);

-- Add comment to explain the policy
COMMENT ON POLICY "Teachers can manage verification settings in their school" ON public.school_settings IS
'Allows teachers (role = teacher) to manage verification method settings for their school.
This includes fields like verification_method_requirement, require_biometric_verification,
and allow_pin_verification which are essential for configuring student attendance verification methods.';
