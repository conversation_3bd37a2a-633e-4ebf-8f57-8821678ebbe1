/**
 * Push Notification Service
 * Handles web push notifications that work even when app is closed
 */

import { supabase } from '@/lib/supabase';
import { audioNotificationService } from './audio-notification-service';

export interface PushNotificationData {
  title: string;
  body: string;
  type: 'attendance_reminder' | 'urgent_reminder' | 'success' | 'warning' | 'info' | 'default';
  icon?: string;
  badge?: string;
  image?: string; // Rich image for modern notifications
  tag?: string;
  requireInteraction?: boolean;
  vibrate?: number[];
  silent?: boolean;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
  data?: any;
}

class PushNotificationService {
  private registration: ServiceWorkerRegistration | null = null;
  private subscription: PushSubscription | null = null;
  private vapidPublicKey: string = 'BH4dSvWC1XstFfiydos43B2PkMcwVJH8WYK__xL5MOh7R2MlARLcuUQA6Uq0WcasVLUp13E9oL5hx5PhSypIFFo';
  private sentNotifications: Set<string> = new Set(); // Track sent notification IDs

  constructor() {
    this.initializeServiceWorker();
  }

  /**
   * Initialize service worker and push notifications
   */
  async initializeServiceWorker(): Promise<void> {
    if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
      console.warn('Push notifications not supported');
      return;
    }

    try {
      // Register service worker
      this.registration = await navigator.serviceWorker.register('/sw.js');
      console.log('Service Worker registered:', this.registration);

      // Wait for service worker to be ready
      await navigator.serviceWorker.ready;

      // Check for existing subscription
      this.subscription = await this.registration.pushManager.getSubscription();
      
      if (this.subscription) {
        console.log('Existing push subscription found');
        await this.savePushSubscription(this.subscription);
      }
    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  }

  /**
   * Request notification permission and subscribe to push notifications
   */
  async requestNotificationPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('Notifications not supported');
      return false;
    }

    // Check current permission
    let permission = Notification.permission;

    // Request permission if not granted
    if (permission === 'default') {
      permission = await Notification.requestPermission();
    }

    if (permission === 'granted') {
      await this.subscribeToPushNotifications();
      return true;
    } else {
      console.warn('Notification permission denied');
      return false;
    }
  }

  /**
   * Subscribe to push notifications
   */
  async subscribeToPushNotifications(): Promise<PushSubscription | null> {
    if (!this.registration) {
      console.error('Service Worker not registered');
      return null;
    }

    try {
      // Convert VAPID key to Uint8Array
      const applicationServerKey = this.urlBase64ToUint8Array(this.vapidPublicKey);

      // Subscribe to push notifications
      this.subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: applicationServerKey
      });

      console.log('Push subscription successful:', this.subscription);

      // Save subscription to backend
      await this.savePushSubscription(this.subscription);

      return this.subscription;
    } catch (error) {
      console.error('Push subscription failed:', error);
      return null;
    }
  }

  /**
   * Save push subscription to backend
   */
  async savePushSubscription(subscription: PushSubscription): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const subscriptionData = {
        user_id: user.id,
        endpoint: subscription.endpoint,
        p256dh_key: subscription.getKey('p256dh') ? btoa(String.fromCharCode(...new Uint8Array(subscription.getKey('p256dh')!))) : null,
        auth_key: subscription.getKey('auth') ? btoa(String.fromCharCode(...new Uint8Array(subscription.getKey('auth')!))) : null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('push_subscriptions')
        .upsert(subscriptionData, { onConflict: 'user_id' });

      if (error) {
        console.error('Error saving push subscription:', error);
      } else {
        console.log('Push subscription saved successfully');
      }
    } catch (error) {
      console.error('Error saving push subscription:', error);
    }
  }

  /**
   * Send a local notification (for testing)
   */
  async sendLocalNotification(data: PushNotificationData): Promise<void> {
    if (!('Notification' in window)) {
      console.error('This browser does not support notifications');
      return;
    }

    if (Notification.permission !== 'granted') {
      console.warn('Notification permission not granted:', Notification.permission);
      return;
    }

    try {
      const notificationOptions = {
        body: data.body,
        icon: data.icon || '/android-chrome-192x192.svg', // Use our app's logo
        badge: data.badge || '/android-chrome-192x192.svg', // Badge for notification area
        image: data.image, // Rich image for modern notifications
        tag: data.tag || `notification-${Date.now()}`,
        requireInteraction: data.requireInteraction || false,
        vibrate: data.vibrate || [200, 100, 200],
        silent: data.silent || false,
        data: data.data,
        // Modern notification styling
        renotify: true,
        timestamp: Date.now(),
        // Add actions for interactive notifications
        actions: data.actions || [],
        // Additional modern features
        dir: 'auto', // Text direction
        lang: 'en', // Language
        // Custom styling (where supported)
        ...(data.type === 'attendance_reminder' && {
          // High priority for attendance reminders
          urgency: 'high'
        })
      };

      const notification = new Notification(data.title, notificationOptions);

      // Add event listeners
      notification.onerror = (error) => {
        console.error('Notification error:', error);
      };

      notification.onclick = () => {
        console.log('Notification clicked');
        window.focus();
        notification.close();

        // Navigate to appropriate page based on notification type
        if (data.type === 'attendance_reminder') {
          window.location.href = '/?tab=scan';
        } else {
          window.location.href = '/?tab=notifications';
        }
      };

      // Only auto-close non-important notifications
      if (data.type === 'info' || data.type === 'default') {
        setTimeout(() => {
          console.log('Auto-closing info notification after 8 seconds');
          notification.close();
        }, 8000);
      }
      // Important notifications (attendance reminders, status changes, etc.) stay visible until user dismisses them



    } catch (error) {
      console.error('Failed to create notification:', error);

      // Fallback: Try using Service Worker registration to show notification
      if (this.registration) {
        console.log('Trying to show notification via Service Worker...');
        try {
          await this.registration.showNotification(data.title, {
            body: data.body,
            icon: data.icon || '/android-chrome-192x192.png',
            badge: data.badge || '/android-chrome-192x192.png',
            tag: data.tag || `sw-notification-${Date.now()}`,
            requireInteraction: data.requireInteraction || false,
            vibrate: data.vibrate || [200, 100, 200],
            silent: data.silent || false,
            data: data.data,
            renotify: true,
            timestamp: Date.now()
          });
          console.log('Service Worker notification shown successfully');
        } catch (swError) {
          console.error('Service Worker notification also failed:', swError);
        }
      }
    }
  }



  /**
   * Send contextual notification based on type and content
   */
  async sendContextualNotification(
    title: string,
    body: string,
    notificationType: string,
    metadata?: any
  ): Promise<void> {
    // Check for duplicate notifications using notification ID
    const notificationId = metadata?.notificationId;
    if (notificationId && this.sentNotifications.has(notificationId)) {
      console.log('Duplicate push notification prevented for ID:', notificationId);
      return;
    }
    // Determine sound type and configuration based on notification type
    let soundType: any = 'default';
    let soundConfig = { repeat: 1, volume: 0.8 };
    let vibrationPattern = [200, 100, 200];
    let requireInteraction = false;
    let actions: Array<{ action: string; title: string }> = [];

    // Map notification types to appropriate sounds and behaviors
    switch (notificationType) {
      case 'attendance':
        if (metadata?.notification_type?.includes('reminder')) {
          soundType = 'attendance_reminder';
          soundConfig = { repeat: 1, volume: 0.9 };
          vibrationPattern = [300, 100, 300, 100, 300, 100, 300];
          requireInteraction = true;
          actions = [
            { action: 'check-in', title: '✅ Check In Now' },
            { action: 'view', title: '👁️ View Details' }
          ];
        } else if (metadata?.status === 'present') {
          soundType = 'marked_present';
          soundConfig = { repeat: 1, volume: 0.8 };
          vibrationPattern = [200, 100, 200];
          actions = [
            { action: 'view', title: '📋 View Attendance' }
          ];
        } else {
          soundType = 'success';
          actions = [
            { action: 'view', title: '📋 View Details' }
          ];
        }
        break;

      case 'absence':
        soundType = 'marked_absent';
        soundConfig = { repeat: 1, volume: 0.7 };
        vibrationPattern = [400, 200, 400];
        requireInteraction = true;
        actions = [
          { action: 'excuse', title: '📝 Request Excuse' },
          { action: 'view', title: '📋 View Details' }
        ];
        break;

      case 'late':
        soundType = 'marked_late';
        soundConfig = { repeat: 1, volume: 0.8 };
        vibrationPattern = [150, 100, 150, 100, 150];
        requireInteraction = true;
        actions = [
          { action: 'excuse', title: '📝 Request Excuse' },
          { action: 'view', title: '📋 View Details' }
        ];
        break;

      case 'excused':
        soundType = 'marked_excused';
        soundConfig = { repeat: 1, volume: 0.7 };
        vibrationPattern = [300, 150, 300];
        requireInteraction = true;
        actions = [
          { action: 'view', title: '📋 View Details' }
        ];
        break;

      case 'excuse_approved':
        soundType = 'excuse_approved';
        soundConfig = { repeat: 1, volume: 0.8 };
        vibrationPattern = [200, 100, 200, 100, 200];
        requireInteraction = true; // Important - student should see this
        actions = [
          { action: 'view', title: 'View Details' }
        ];
        break;

      case 'excuse_rejected':
        soundType = 'excuse_rejected';
        soundConfig = { repeat: 1, volume: 0.7 };
        vibrationPattern = [400, 200, 400];
        requireInteraction = true; // Important - student should see this
        actions = [
          { action: 'view', title: 'View Details' }
        ];
        break;

      case 'system':
        soundType = 'system_update';
        soundConfig = { repeat: 1, volume: 0.6 };
        vibrationPattern = [200, 100, 200];
        break;

      default:
        soundType = 'default';
        soundConfig = { repeat: 1, volume: 0.7 };
        vibrationPattern = [200, 100, 200];
    }

    // Play audio notification first
    try {
      await audioNotificationService.playNotificationSound(soundType, soundConfig);
    } catch (audioError) {
      console.warn('Failed to play audio notification:', audioError);
    }

    // Generate unique tag using notification ID to prevent duplicates
    const uniqueTag = metadata?.notificationId
      ? `notification-${metadata.notificationId}`
      : `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Determine icon and badge based on notification type for better visual distinction
    // Use PNG for better compatibility, fallback to SVG
    let icon = '/android-chrome-192x192.png'; // Default app logo (PNG for compatibility)
    let badge = '/android-chrome-192x192.png'; // Default badge
    let image = undefined;

    // For now, use SVG files directly (they work well on modern browsers)
    // TODO: Generate PNG files for better compatibility with older devices
    const defaultIcon = '/android-chrome-192x192.svg';
    const defaultBadge = '/android-chrome-192x192.svg';

    switch (notificationType) {
      case 'attendance':
        if (metadata?.notification_type?.includes('reminder')) {
          icon = '/android-chrome-512x512.svg'; // Larger icon for important reminders
          image = '/logo-horizontal.svg'; // Rich image for reminders
        } else {
          icon = defaultIcon;
        }
        break;
      case 'absence':
      case 'late':
      case 'excused':
      case 'system':
      default:
        icon = defaultIcon;
        break;
    }

    badge = defaultBadge;

    // Then send the visual push notification with unique tag and modern styling
    await this.sendLocalNotification({
      title,
      body,
      type: notificationType as any,
      tag: uniqueTag, // This prevents duplicate notifications with same tag
      icon, // Custom icon based on notification type
      badge, // Badge for notification area
      image, // Rich image for supported platforms
      requireInteraction,
      vibrate: vibrationPattern,
      silent: false,
      actions,
      data: {
        notificationType,
        metadata,
        notificationId: metadata?.notificationId,
        timestamp: metadata?.timestamp || new Date().toISOString()
      }
    });

    // Mark this notification as sent to prevent duplicates
    if (metadata?.notificationId) {
      this.sentNotifications.add(metadata.notificationId);

      // Clean up old notification IDs to prevent memory leaks (keep only last 100)
      if (this.sentNotifications.size > 100) {
        const oldestIds = Array.from(this.sentNotifications).slice(0, this.sentNotifications.size - 100);
        oldestIds.forEach(id => this.sentNotifications.delete(id));
      }
    }
  }

  /**
   * Send attendance reminder notification (legacy method for backward compatibility)
   */
  async sendAttendanceReminderNotification(roomName: string, teacherName: string): Promise<void> {
    await this.sendContextualNotification(
      '⏰ Attendance Reminder',
      `${teacherName} is requesting attendance verification for ${roomName}. Please check in now!`,
      'attendance',
      {
        notification_type: 'attendance_reminder',
        teacher_name: teacherName,
        room_name: roomName
      }
    );
  }

  /**
   * Check if an image file exists
   */
  private async checkImageExists(url: string): Promise<boolean> {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch {
      return false;
    }
  }

  /**
   * Clear sent notifications cache (useful for testing or cleanup)
   */
  clearSentNotificationsCache(): void {
    this.sentNotifications.clear();
    console.log('Sent notifications cache cleared');
  }

  /**
   * Get count of tracked sent notifications
   */
  getSentNotificationsCount(): number {
    return this.sentNotifications.size;
  }

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribe(): Promise<boolean> {
    if (!this.subscription) {
      return true;
    }

    try {
      const success = await this.subscription.unsubscribe();
      
      if (success) {
        // Remove from backend
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          await supabase
            .from('push_subscriptions')
            .delete()
            .eq('user_id', user.id);
        }
        
        this.subscription = null;
        console.log('Push subscription removed');
      }
      
      return success;
    } catch (error) {
      console.error('Error unsubscribing from push notifications:', error);
      return false;
    }
  }

  /**
   * Check if push notifications are supported and enabled
   */
  isSupported(): boolean {
    return 'serviceWorker' in navigator && 'PushManager' in window && 'Notification' in window;
  }

  /**
   * Get current notification permission status
   */
  getPermissionStatus(): NotificationPermission {
    return Notification.permission;
  }

  /**
   * Check if currently subscribed to push notifications
   */
  isSubscribed(): boolean {
    return this.subscription !== null;
  }

  /**
   * Utility function to convert VAPID key
   */
  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }
}

// Export singleton instance
export const pushNotificationService = new PushNotificationService();
export default pushNotificationService;
