# Attendance Tracking System

A modern, secure attendance tracking system for educational institutions with biometric authentication, QR code scanning, and comprehensive management features.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or bun
- Supabase account

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd attendance-tracking-system

# Install dependencies
npm install

# Set up environment variables
cp environments/.env.example .env
# Edit .env with your Supabase credentials

# Start development server
npm run dev
```

## 📁 Project Structure

```
attendance-tracking-system/
├── 📁 src/                    # Application source code
├── 📁 docs/                   # Documentation (organized by category)
├── 📁 scripts/                # Build & deployment scripts
├── 📁 database/               # Database migrations & functions
├── 📁 environments/           # Environment configurations
├── 📁 tools/                  # Development utilities
└── 📁 supabase/               # Supabase configuration
```

## 🎯 Features

- **Smart Attendance Tracking** - QR code and biometric authentication
- **Multi-Role Support** - Students, teachers, admins, and system administrators
- **Real-time Analytics** - Comprehensive dashboards and reporting
- **Anti-Fraud Measures** - Location verification and behavioral analysis
- **Internationalization** - Support for multiple languages (English/Turkish)
- **Social Media Integration** - School updates and announcements
- **Mobile Responsive** - Works on all devices and screen sizes

## 📚 Documentation

- **[Deployment Guide](docs/deployment/)** - Production setup and configuration
- **[Development Guide](docs/development/)** - Local development and contribution
- **[Feature Documentation](docs/features/)** - Detailed feature explanations
- **[User Guides](docs/user-guides/)** - End-user documentation

## 🛠️ Development

### Environment Setup
```bash
# Development with hot reload
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### Testing
```bash
# Run tests
npm run test

# Run tests with coverage
npm run test:coverage
```

## 🚀 Deployment

See [Production Deployment Guide](docs/deployment/PRODUCTION_DEPLOYMENT_GUIDE.md) for detailed deployment instructions.

### Quick Deploy
```bash
# Build for production
npm run build

# Deploy to your hosting platform
# (Vercel, Netlify, or custom server)
```

## 🔧 Configuration

Environment variables are managed in the `environments/` directory:
- `.env.development` - Development configuration
- `.env.production` - Production configuration
- `.env.example` - Template for new environments

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

See [Development Guide](docs/development/) for detailed contribution guidelines.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: Check the `docs/` directory
- **Issues**: Create an issue on GitHub
- **Questions**: Contact the development team

## 🏗️ Built With

- **Frontend**: React + TypeScript + Vite
- **Styling**: Tailwind CSS + shadcn/ui
- **Backend**: Supabase (PostgreSQL + Auth + Storage)
- **Authentication**: Supabase Auth + WebAuthn
- **Deployment**: Vercel/Netlify compatible
