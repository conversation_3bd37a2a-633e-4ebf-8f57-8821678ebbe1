import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Fingerprint,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Building,
  Calendar,
  MessageSquare,
  Eye,
  Loader2,
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";
import { supabase } from "@/lib/supabase";
import { formatDistanceToNow } from "date-fns";
import { enUS, tr } from "date-fns/locale";

interface BiometricRequest {
  id: string;
  user_id: string;
  student_name: string;
  student_email: string;
  student_number?: string;
  block_name?: string;
  room_name?: string;
  request_reason?: string;
  status: 'pending' | 'approved' | 'rejected';
  admin_notes?: string;
  approved_by?: string;
  approved_at?: string;
  rejected_by?: string;
  rejected_at?: string;
  created_at: string;
  updated_at: string;
}

export default function BiometricRegistrationRequests() {
  const [requests, setRequests] = useState<BiometricRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<BiometricRequest | null>(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [adminNotes, setAdminNotes] = useState("");
  const [processing, setProcessing] = useState(false);

  const { profile } = useAuth();
  const { toast } = useToast();
  const { t, i18n } = useTranslation();

  // Fetch biometric registration requests
  useEffect(() => {
    const fetchRequests = async () => {
      if (!profile?.school_id) return;

      setLoading(true);
      try {
        const { data, error } = await supabase
          .from("biometric_registration_requests")
          .select("*")
          .eq("school_id", profile.school_id)
          .order("created_at", { ascending: false });

        if (error) throw error;

        setRequests(data || []);
      } catch (error) {
        console.error("Error fetching biometric requests:", error);
        toast({
          title: t("admin.biometricRequests.errorLoading"),
          description: t("admin.biometricRequests.errorLoadingMessage"),
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchRequests();
  }, [profile?.school_id, toast, t]);

  // Handle approve request
  const handleApprove = async () => {
    if (!selectedRequest) return;

    setProcessing(true);
    try {
      const { error } = await supabase.rpc("approve_biometric_request", {
        request_id: selectedRequest.id,
        admin_notes_param: adminNotes || null,
      });

      if (error) throw error;

      // Update local state
      setRequests(prev => prev.map(req => 
        req.id === selectedRequest.id 
          ? { ...req, status: 'approved', admin_notes: adminNotes, approved_at: new Date().toISOString() }
          : req
      ));

      toast({
        title: t("admin.biometricRequests.requestApproved"),
        description: t("admin.biometricRequests.studentCanNowRegister"),
      });

      setShowApproveDialog(false);
      setSelectedRequest(null);
      setAdminNotes("");
    } catch (error) {
      console.error("Error approving request:", error);
      toast({
        title: t("admin.biometricRequests.errorApproving"),
        description: t("admin.biometricRequests.errorApprovingMessage"),
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  // Handle reject request
  const handleReject = async () => {
    if (!selectedRequest) return;

    setProcessing(true);
    try {
      const { error } = await supabase.rpc("reject_biometric_request", {
        request_id: selectedRequest.id,
        admin_notes_param: adminNotes || null,
      });

      if (error) throw error;

      // Update local state
      setRequests(prev => prev.map(req => 
        req.id === selectedRequest.id 
          ? { ...req, status: 'rejected', admin_notes: adminNotes, rejected_at: new Date().toISOString() }
          : req
      ));

      toast({
        title: t("admin.biometricRequests.requestRejected"),
        description: t("admin.biometricRequests.studentNotified"),
      });

      setShowRejectDialog(false);
      setSelectedRequest(null);
      setAdminNotes("");
    } catch (error) {
      console.error("Error rejecting request:", error);
      toast({
        title: t("admin.biometricRequests.errorRejecting"),
        description: t("admin.biometricRequests.errorRejectingMessage"),
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge variant="outline" className="border-yellow-500 text-yellow-700">
            <Clock className="w-3 h-3 mr-1" />
            {t("admin.biometricRequests.pending")}
          </Badge>
        );
      case 'approved':
        return (
          <Badge variant="outline" className="border-green-500 text-green-700">
            <CheckCircle className="w-3 h-3 mr-1" />
            {t("admin.biometricRequests.approved")}
          </Badge>
        );
      case 'rejected':
        return (
          <Badge variant="outline" className="border-red-500 text-red-700">
            <XCircle className="w-3 h-3 mr-1" />
            {t("admin.biometricRequests.rejected")}
          </Badge>
        );
      default:
        return null;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const locale = i18n.language === 'tr' ? tr : enUS;
    return formatDistanceToNow(new Date(dateString), { 
      addSuffix: true, 
      locale 
    });
  };

  const pendingRequests = requests.filter(req => req.status === 'pending');
  const processedRequests = requests.filter(req => req.status !== 'pending');

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Fingerprint className="h-5 w-5" />
            {t("admin.biometricRequests.title")}
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            {t("admin.biometricRequests.description")}
          </p>
        </CardHeader>
      </Card>

      {/* Pending Requests */}
      {pendingRequests.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <Clock className="h-5 w-5 text-yellow-600" />
              {t("admin.biometricRequests.pendingRequests")} ({pendingRequests.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("admin.biometricRequests.student")}</TableHead>
                  <TableHead>{t("admin.biometricRequests.location")}</TableHead>
                  <TableHead>{t("admin.biometricRequests.requestDate")}</TableHead>
                  <TableHead>{t("admin.biometricRequests.actions")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {pendingRequests.map((request) => (
                  <TableRow key={request.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{request.student_name}</div>
                        <div className="text-sm text-muted-foreground">{request.student_email}</div>
                        {request.student_number && (
                          <div className="text-xs text-muted-foreground">#{request.student_number}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {request.block_name && <div>{request.block_name}</div>}
                        {request.room_name && <div className="text-muted-foreground">{request.room_name}</div>}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">{formatDate(request.created_at)}</div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            setSelectedRequest(request);
                            setShowDetailsDialog(true);
                          }}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          {t("admin.biometricRequests.view")}
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => {
                            setSelectedRequest(request);
                            setShowApproveDialog(true);
                          }}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          {t("admin.biometricRequests.approve")}
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => {
                            setSelectedRequest(request);
                            setShowRejectDialog(true);
                          }}
                        >
                          <XCircle className="h-4 w-4 mr-1" />
                          {t("admin.biometricRequests.reject")}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* No Pending Requests */}
      {pendingRequests.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <Fingerprint className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">{t("admin.biometricRequests.noPendingRequests")}</h3>
            <p className="text-muted-foreground">{t("admin.biometricRequests.noPendingRequestsMessage")}</p>
          </CardContent>
        </Card>
      )}

      {/* Processed Requests History */}
      {processedRequests.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-lg">
              <MessageSquare className="h-5 w-5 text-muted-foreground" />
              {t("admin.biometricRequests.processedRequests")} ({processedRequests.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t("admin.biometricRequests.student")}</TableHead>
                  <TableHead>{t("admin.biometricRequests.status")}</TableHead>
                  <TableHead>{t("admin.biometricRequests.processedDate")}</TableHead>
                  <TableHead>{t("admin.biometricRequests.actions")}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {processedRequests.map((request) => (
                  <TableRow key={request.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{request.student_name}</div>
                        <div className="text-sm text-muted-foreground">{request.student_email}</div>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(request.status)}</TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {formatDate(request.approved_at || request.rejected_at || request.updated_at)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setSelectedRequest(request);
                          setShowDetailsDialog(true);
                        }}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        {t("admin.biometricRequests.view")}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Request Details Dialog */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              {t("admin.biometricRequests.requestDetails")}
            </DialogTitle>
            <DialogDescription>
              {t("admin.biometricRequests.requestDetailsDescription")}
            </DialogDescription>
          </DialogHeader>

          {selectedRequest && (
            <div className="space-y-4">
              {/* Student Information */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">{t("admin.biometricRequests.studentName")}</label>
                  <p className="text-sm text-muted-foreground">{selectedRequest.student_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">{t("admin.biometricRequests.studentEmail")}</label>
                  <p className="text-sm text-muted-foreground">{selectedRequest.student_email}</p>
                </div>
                {selectedRequest.student_number && (
                  <div>
                    <label className="text-sm font-medium">{t("admin.biometricRequests.studentNumber")}</label>
                    <p className="text-sm text-muted-foreground">#{selectedRequest.student_number}</p>
                  </div>
                )}
                <div>
                  <label className="text-sm font-medium">{t("admin.biometricRequests.status")}</label>
                  <div className="mt-1">{getStatusBadge(selectedRequest.status)}</div>
                </div>
              </div>

              {/* Location Information */}
              {(selectedRequest.block_name || selectedRequest.room_name) && (
                <div>
                  <label className="text-sm font-medium">{t("admin.biometricRequests.location")}</label>
                  <div className="text-sm text-muted-foreground">
                    {selectedRequest.block_name && <div>{selectedRequest.block_name}</div>}
                    {selectedRequest.room_name && <div>{selectedRequest.room_name}</div>}
                  </div>
                </div>
              )}

              {/* Request Reason */}
              {selectedRequest.request_reason && (
                <div>
                  <label className="text-sm font-medium">{t("admin.biometricRequests.requestReason")}</label>
                  <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
                    {selectedRequest.request_reason}
                  </p>
                </div>
              )}

              {/* Admin Notes */}
              {selectedRequest.admin_notes && (
                <div>
                  <label className="text-sm font-medium">{t("admin.biometricRequests.adminNotes")}</label>
                  <p className="text-sm text-muted-foreground bg-muted p-3 rounded-md">
                    {selectedRequest.admin_notes}
                  </p>
                </div>
              )}

              {/* Timestamps */}
              <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
                <div>
                  <label className="font-medium">{t("admin.biometricRequests.requestDate")}</label>
                  <p>{formatDate(selectedRequest.created_at)}</p>
                </div>
                {(selectedRequest.approved_at || selectedRequest.rejected_at) && (
                  <div>
                    <label className="font-medium">{t("admin.biometricRequests.processedDate")}</label>
                    <p>{formatDate(selectedRequest.approved_at || selectedRequest.rejected_at || "")}</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Approve Dialog */}
      <AlertDialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              {t("admin.biometricRequests.approveRequest")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("admin.biometricRequests.approveRequestDescription")}
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">{t("admin.biometricRequests.adminNotes")} ({t("common.optional")})</label>
              <Textarea
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                placeholder={t("admin.biometricRequests.adminNotesPlaceholder")}
                className="mt-1"
              />
            </div>
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setAdminNotes("")}>
              {t("common.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleApprove}
              disabled={processing}
              className="bg-green-600 hover:bg-green-700"
            >
              {processing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {t("admin.biometricRequests.approving")}
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {t("admin.biometricRequests.approve")}
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Reject Dialog */}
      <AlertDialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-600" />
              {t("admin.biometricRequests.rejectRequest")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("admin.biometricRequests.rejectRequestDescription")}
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">{t("admin.biometricRequests.rejectionReason")} ({t("common.optional")})</label>
              <Textarea
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                placeholder={t("admin.biometricRequests.rejectionReasonPlaceholder")}
                className="mt-1"
              />
            </div>
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setAdminNotes("")}>
              {t("common.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleReject}
              disabled={processing}
              className="bg-red-600 hover:bg-red-700"
            >
              {processing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {t("admin.biometricRequests.rejecting")}
                </>
              ) : (
                <>
                  <XCircle className="h-4 w-4 mr-2" />
                  {t("admin.biometricRequests.reject")}
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
