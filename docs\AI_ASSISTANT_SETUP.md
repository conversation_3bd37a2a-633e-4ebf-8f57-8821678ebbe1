# AI Assistant Setup Guide

## Overview

The ATS Assistant is now powered by **DeepSeek R1**, a state-of-the-art AI model, accessed through OpenRouter's free API. This provides intelligent, contextual responses instead of simple pattern matching.

## Features

✨ **Intelligent Responses**: Real AI understanding of user queries
🔄 **Automatic Fallback**: Falls back to pattern matching if API is unavailable
🎯 **Contextual Awareness**: Understands user role, school context, and conversation history
🚀 **Free API**: Uses DeepSeek R1 through OpenRouter's free tier
📱 **Smart Actions**: Generates relevant quick action buttons based on conversation

## Setup Instructions

### 1. Get Your Free OpenRouter API Key

1. Visit [OpenRouter.ai](https://openrouter.ai/)
2. Sign up for a free account
3. Navigate to the API Keys section
4. Create a new API key
5. Copy the API key (starts with `sk-or-v1-...`)

### 2. Configure the Environment Variable

Add your API key to the `.env` file:

```bash
# AI Assistant Configuration (OpenRouter - DeepSeek R1)
VITE_OPENROUTER_API_KEY=sk-or-v1-your-actual-api-key-here
```

### 3. Restart the Development Server

```bash
npm run dev
```

## How It Works

### AI-Powered Mode (When API Key is Available)
- Uses DeepSeek R1 for intelligent responses
- Provides contextual understanding
- Generates smart quick actions
- Remembers conversation history
- Adapts to user role and school context

### Fallback Mode (When API is Unavailable)
- Uses sophisticated pattern matching
- Provides predefined responses
- Still functional but less intelligent
- Automatically switches back when API is available

## API Usage and Costs

- **DeepSeek R1**: Free tier available on OpenRouter
- **Rate Limits**: Generous free tier limits
- **Fallback**: Always available even without API key
- **Cost**: $0 for typical usage on free tier

## Customization

### System Prompt
The AI assistant's personality and knowledge are defined in the system prompt within `ai-assistant-service.ts`. You can customize:

- Response style and tone
- Knowledge about your specific school
- Specialized instructions for different user roles
- Integration with your specific features

### Context Integration
The assistant automatically receives:
- User role (student/teacher/admin)
- Current page/section
- School information
- Recent conversation history
- User preferences

## Troubleshooting

### API Key Issues
- Ensure the API key starts with `sk-or-v1-`
- Check that the key is correctly set in `.env`
- Restart the development server after adding the key

### Network Issues
- The assistant automatically falls back to pattern matching
- Check browser console for API error messages
- Verify internet connectivity

### Response Quality
- The AI learns from conversation context
- Provide clear, specific questions for best results
- Use the quick action buttons for common tasks

## Security Notes

- API keys are only used client-side for this demo
- In production, consider server-side API calls
- OpenRouter provides usage monitoring and controls
- The fallback system ensures functionality without API dependency

## Support

For issues with:
- **OpenRouter API**: Contact OpenRouter support
- **ATS Assistant**: Check the browser console for error messages
- **Integration**: Review the `ai-assistant-service.ts` file

---

🎉 **Enjoy your intelligent AI assistant!** The ATS Assistant is now powered by cutting-edge AI technology while maintaining reliability through smart fallback mechanisms.
