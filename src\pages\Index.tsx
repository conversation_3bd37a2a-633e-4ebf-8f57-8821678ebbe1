import { useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Navbar from "@/components/shared/Navbar";
import Footer from "@/components/shared/Footer";
import {
  ShieldCheck,
  Users,
  BarChart4,
  CheckCircle,
  Clock,
  ClipboardCheck,
  QrCode,
  Fingerprint,
  MapPin,
  Smartphone,
  Globe,
  Zap,
  Shield,
  TrendingUp,
  MessageSquare,
  Calendar,
  FileText,
  Settings,
  Database,
  Wifi,
  Star,
  ArrowRight,
  Play,
  Monitor,
  UserCheck,
  Bell,
  Lock,
  Palette,
  Languages,
  Cloud,
  RefreshCw,
} from "lucide-react";
import { useBranding } from "@/hooks/useBranding";
import { Logo } from "@/components/ui/logo";
import { useTranslation } from "react-i18next";

export default function Index() {
  const navigate = useNavigate();
  const { branding } = useBranding();
  const { t } = useTranslation();

  useEffect(() => {
    // Check if user is already logged in and redirect to their dashboard
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      const user = JSON.parse(storedUser);
      navigate(`/${user.role}`);
    }
  }, [navigate]);

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative min-h-screen flex items-center bg-gradient-to-br from-primary via-primary/95 to-secondary text-white overflow-hidden dark:from-[#F39228] dark:via-[#F39228]/95 dark:to-[#F39228]/80">
          {/* Animated Background Elements */}
          <div className="absolute inset-0">
            <div className="absolute inset-0 bg-grid-white/[0.03] bg-[size:80px_80px]" />
            <div className="absolute inset-0 bg-gradient-to-t from-primary/30 via-transparent to-transparent" />
            {/* Floating Orbs */}
            <div className="absolute top-20 left-10 w-72 h-72 bg-white/5 rounded-full blur-3xl animate-pulse" />
            <div className="absolute bottom-20 right-10 w-96 h-96 bg-white/3 rounded-full blur-3xl animate-pulse delay-1000" />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-white/2 rounded-full blur-3xl animate-pulse delay-500" />
          </div>

          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-6xl mx-auto">
              {/* Top Badge */}
              <div className="text-center mb-8 sm:mb-12 mt-8 sm:mt-12">
                <Badge variant="secondary" className="inline-flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 bg-white/10 text-white border-white/20 hover:bg-white/20 backdrop-blur-sm text-xs sm:text-sm font-medium rounded-full transition-all duration-300 hover:scale-105">
                  <Star className="w-3 h-3 sm:w-4 sm:h-4" />
                  <span className="whitespace-nowrap">{t('landing.hero.badge')}</span>
                  <Zap className="w-3 h-3 sm:w-4 sm:h-4" />
                </Badge>
              </div>

              {/* Logo */}
              <div className="text-center mb-8 sm:mb-12">
                <div className="inline-block p-4 sm:p-6 bg-white/10 backdrop-blur-sm rounded-2xl sm:rounded-3xl shadow-2xl hover:scale-105 transition-transform duration-300">
                  <div className="block sm:hidden">
                    <Logo
                      variant="square"
                      size="lg"
                      className="justify-center"
                    />
                  </div>
                  <div className="hidden sm:block">
                    <Logo
                      variant="square"
                      size="xl"
                      className="justify-center"
                    />
                  </div>
                </div>
              </div>

              {/* Main Heading */}
              <div className="text-center mb-12">
                <h1 className="text-5xl md:text-7xl lg:text-8xl font-black mb-6 leading-[0.9] tracking-tight">
                  <span className="block mb-4 bg-gradient-to-r from-white via-white to-white/90 bg-clip-text text-transparent">
                    {t('landing.hero.title1')}
                  </span>
                  <span className="block bg-gradient-to-r from-white/95 via-white/80 to-white/70 bg-clip-text text-transparent">
                    {t('landing.hero.title2')}
                  </span>
                </h1>

                {/* Subtitle */}
                <p className="text-xl md:text-2xl lg:text-3xl mb-12 max-w-5xl mx-auto text-white/85 leading-relaxed font-light">
                  {t('landing.hero.subtitle')}
                </p>
              </div>

              {/* Feature Highlights */}
              <div className="flex flex-wrap justify-center gap-4 mb-16">
                <Badge variant="secondary" className="px-4 py-2 bg-white/10 text-white border-white/20 hover:bg-white/20 backdrop-blur-sm transition-all duration-300 hover:scale-105">
                  <QrCode className="w-5 h-5 mr-2" />
                  {t('landing.hero.features.qr')}
                </Badge>
                <Badge variant="secondary" className="px-4 py-2 bg-white/10 text-white border-white/20 hover:bg-white/20 backdrop-blur-sm transition-all duration-300 hover:scale-105">
                  <Fingerprint className="w-5 h-5 mr-2" />
                  {t('landing.hero.features.biometric')}
                </Badge>
                <Badge variant="secondary" className="px-4 py-2 bg-white/10 text-white border-white/20 hover:bg-white/20 backdrop-blur-sm transition-all duration-300 hover:scale-105">
                  <MapPin className="w-5 h-5 mr-2" />
                  {t('landing.hero.features.location')}
                </Badge>
                <Badge variant="secondary" className="px-4 py-2 bg-white/10 text-white border-white/20 hover:bg-white/20 backdrop-blur-sm transition-all duration-300 hover:scale-105">
                  <Globe className="w-5 h-5 mr-2" />
                  {t('landing.hero.features.multilingual')}
                </Badge>
              </div>

              {/* CTA Buttons */}
              <div className="text-center mb-20">
                <div className="flex flex-col sm:flex-row justify-center gap-6 max-w-lg mx-auto">
                  <Button
                    size="lg"
                    className="h-14 px-8 bg-white hover:bg-gray-50 shadow-2xl hover:shadow-3xl transition-all duration-300 hover:scale-105 text-lg font-semibold rounded-lg"
                    asChild
                  >
                    <Link to="/login" className="text-red-600 hover:text-red-700 flex items-center justify-center">
                      {t('landing.getStarted')}
                      <ArrowRight className="w-6 h-6 ml-2" />
                    </Link>
                  </Button>
                  <Button
                    size="lg"
                    variant="outline"
                    className="h-14 px-8 border-2 border-white bg-transparent hover:bg-white/10 backdrop-blur-sm transition-all duration-300 hover:scale-105 text-lg font-semibold rounded-lg"
                    onClick={() => {
                      const featuresSection = document.getElementById('features');
                      if (featuresSection) {
                        featuresSection.scrollIntoView({
                          behavior: 'smooth',
                          block: 'start'
                        });
                      }
                    }}
                  >
                    <Play className="w-6 h-6 mr-2" />
                    {t('landing.hero.learnMore')}
                  </Button>
                </div>
              </div>

              {/* Stats Section */}
              <div className="relative">
                <div className="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-3xl" />
                <div className="relative grid grid-cols-2 lg:grid-cols-4 gap-8 p-8 lg:p-12">
                  <div className="text-center group">
                    <div className="text-4xl lg:text-5xl font-black mb-3 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300">
                      99.9%
                    </div>
                    <div className="text-white/70 font-medium text-sm lg:text-base">
                      {t('landing.stats.accuracy')}
                    </div>
                  </div>
                  <div className="text-center group">
                    <div className="text-4xl lg:text-5xl font-black mb-3 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300">
                      &lt;2s
                    </div>
                    <div className="text-white/70 font-medium text-sm lg:text-base">
                      {t('landing.stats.speed')}
                    </div>
                  </div>
                  <div className="text-center group">
                    <div className="text-4xl lg:text-5xl font-black mb-3 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300">
                      24/7
                    </div>
                    <div className="text-white/70 font-medium text-sm lg:text-base">
                      {t('landing.stats.availability')}
                    </div>
                  </div>
                  <div className="text-center group">
                    <div className="text-4xl lg:text-5xl font-black mb-3 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent group-hover:scale-110 transition-transform duration-300">
                      100+
                    </div>
                    <div className="text-white/70 font-medium text-sm lg:text-base">
                      {t('landing.stats.schools')}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Scroll Indicator */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
              <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse" />
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-20 bg-gray-50 dark:bg-gray-900/50">
          <div className="container mx-auto px-4">
            {/* Section Header */}
            <div className="text-center mb-16">
              <Badge variant="outline" className="mb-4">
                {t('landing.features.badge')}
              </Badge>
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                {t('landing.features.title')}
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                {t('landing.features.subtitle')}
              </p>
            </div>

            {/* Core Features Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              {/* QR Code Attendance */}
              <Card className="group hover:shadow-lg transition-all duration-300 border-0 shadow-md">
                <CardHeader>
                  <div className="flex items-center justify-center h-14 w-14 bg-primary/10 text-primary rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    <QrCode size={28} />
                  </div>
                  <CardTitle className="text-xl">{t('landing.features.qr.title')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base leading-relaxed">
                    {t('landing.features.qr.description')}
                  </CardDescription>
                </CardContent>
              </Card>

              {/* Biometric Authentication */}
              <Card className="group hover:shadow-lg transition-all duration-300 border-0 shadow-md">
                <CardHeader>
                  <div className="flex items-center justify-center h-14 w-14 bg-secondary/10 text-secondary rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Fingerprint size={28} />
                  </div>
                  <CardTitle className="text-xl">{t('landing.features.biometric.title')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base leading-relaxed">
                    {t('landing.features.biometric.description')}
                  </CardDescription>
                </CardContent>
              </Card>

              {/* Location Verification */}
              <Card className="group hover:shadow-lg transition-all duration-300 border-0 shadow-md">
                <CardHeader>
                  <div className="flex items-center justify-center h-14 w-14 bg-green-100 text-green-600 rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    <MapPin size={28} />
                  </div>
                  <CardTitle className="text-xl">{t('landing.features.location.title')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base leading-relaxed">
                    {t('landing.features.location.description')}
                  </CardDescription>
                </CardContent>
              </Card>

              {/* Real-time Analytics */}
              <Card className="group hover:shadow-lg transition-all duration-300 border-0 shadow-md">
                <CardHeader>
                  <div className="flex items-center justify-center h-14 w-14 bg-blue-100 text-blue-600 rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    <TrendingUp size={28} />
                  </div>
                  <CardTitle className="text-xl">{t('landing.features.analytics.title')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base leading-relaxed">
                    {t('landing.features.analytics.description')}
                  </CardDescription>
                </CardContent>
              </Card>

              {/* Multi-language Support */}
              <Card className="group hover:shadow-lg transition-all duration-300 border-0 shadow-md">
                <CardHeader>
                  <div className="flex items-center justify-center h-14 w-14 bg-purple-100 text-purple-600 rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Languages size={28} />
                  </div>
                  <CardTitle className="text-xl">{t('landing.features.multilingual.title')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base leading-relaxed">
                    {t('landing.features.multilingual.description')}
                  </CardDescription>
                </CardContent>
              </Card>

              {/* Excuse Management */}
              <Card className="group hover:shadow-lg transition-all duration-300 border-0 shadow-md">
                <CardHeader>
                  <div className="flex items-center justify-center h-14 w-14 bg-orange-100 text-orange-600 rounded-xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    <FileText size={28} />
                  </div>
                  <CardTitle className="text-xl">{t('landing.features.excuses.title')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base leading-relaxed">
                    {t('landing.features.excuses.description')}
                  </CardDescription>
                </CardContent>
              </Card>
            </div>

            {/* Advanced Features */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg">
              <h3 className="text-2xl font-bold text-center mb-8">{t('landing.features.advanced.title')}</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="flex items-center justify-center h-12 w-12 bg-primary/10 text-primary rounded-lg mb-3 mx-auto">
                    <Shield size={24} />
                  </div>
                  <h4 className="font-semibold mb-1">{t('landing.features.advanced.security')}</h4>
                  <p className="text-sm text-muted-foreground">{t('landing.features.advanced.securityDesc')}</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center h-12 w-12 bg-primary/10 text-primary rounded-lg mb-3 mx-auto">
                    <Bell size={24} />
                  </div>
                  <h4 className="font-semibold mb-1">{t('landing.features.advanced.notifications')}</h4>
                  <p className="text-sm text-muted-foreground">{t('landing.features.advanced.notificationsDesc')}</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center h-12 w-12 bg-primary/10 text-primary rounded-lg mb-3 mx-auto">
                    <Palette size={24} />
                  </div>
                  <h4 className="font-semibold mb-1">{t('landing.features.advanced.branding')}</h4>
                  <p className="text-sm text-muted-foreground">{t('landing.features.advanced.brandingDesc')}</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center h-12 w-12 bg-primary/10 text-primary rounded-lg mb-3 mx-auto">
                    <Cloud size={24} />
                  </div>
                  <h4 className="font-semibold mb-1">{t('landing.features.advanced.cloud')}</h4>
                  <p className="text-sm text-muted-foreground">{t('landing.features.advanced.cloudDesc')}</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How it Works */}
        <section className="py-20 bg-white dark:bg-gray-900/20">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <Badge variant="outline" className="mb-4">
                {t('landing.howItWorks.badge')}
              </Badge>
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                {t('landing.howItWorks.title')}
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                {t('landing.howItWorks.subtitle')}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
              {/* Step 1: Setup */}
              <div className="flex flex-col items-center text-center">
                <div className="relative mb-6">
                  <div className="flex items-center justify-center h-16 w-16 bg-primary/10 text-primary rounded-2xl mb-4 mx-auto">
                    <Settings size={32} />
                  </div>
                  <div className="absolute -top-2 -right-2 bg-primary text-white text-sm font-bold rounded-full h-8 w-8 flex items-center justify-center">
                    1
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-3">
                  {t('landing.howItWorks.step1.title')}
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  {t('landing.howItWorks.step1.description')}
                </p>
              </div>

              {/* Step 2: Configure */}
              <div className="flex flex-col items-center text-center">
                <div className="relative mb-6">
                  <div className="flex items-center justify-center h-16 w-16 bg-secondary/10 text-secondary rounded-2xl mb-4 mx-auto">
                    <Monitor size={32} />
                  </div>
                  <div className="absolute -top-2 -right-2 bg-secondary text-white text-sm font-bold rounded-full h-8 w-8 flex items-center justify-center">
                    2
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-3">
                  {t('landing.howItWorks.step2.title')}
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  {t('landing.howItWorks.step2.description')}
                </p>
              </div>

              {/* Step 3: Track */}
              <div className="flex flex-col items-center text-center">
                <div className="relative mb-6">
                  <div className="flex items-center justify-center h-16 w-16 bg-green-100 text-green-600 rounded-2xl mb-4 mx-auto">
                    <UserCheck size={32} />
                  </div>
                  <div className="absolute -top-2 -right-2 bg-green-600 text-white text-sm font-bold rounded-full h-8 w-8 flex items-center justify-center">
                    3
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-3">
                  {t('landing.howItWorks.step3.title')}
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  {t('landing.howItWorks.step3.description')}
                </p>
              </div>

              {/* Step 4: Analyze */}
              <div className="flex flex-col items-center text-center">
                <div className="relative mb-6">
                  <div className="flex items-center justify-center h-16 w-16 bg-blue-100 text-blue-600 rounded-2xl mb-4 mx-auto">
                    <BarChart4 size={32} />
                  </div>
                  <div className="absolute -top-2 -right-2 bg-blue-600 text-white text-sm font-bold rounded-full h-8 w-8 flex items-center justify-center">
                    4
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-3">
                  {t('landing.howItWorks.step4.title')}
                </h3>
                <p className="text-muted-foreground leading-relaxed">
                  {t('landing.howItWorks.step4.description')}
                </p>
              </div>
            </div>

            {/* Process Flow Visualization */}
            <div className="bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-center mb-8">{t('landing.howItWorks.process.title')}</h3>
              <div className="flex flex-col md:flex-row items-center justify-between gap-6">
                <div className="flex items-center gap-4">
                  <div className="flex items-center justify-center h-12 w-12 bg-primary text-white rounded-lg">
                    <Smartphone size={24} />
                  </div>
                  <div>
                    <h4 className="font-semibold">{t('landing.howItWorks.process.student')}</h4>
                    <p className="text-sm text-muted-foreground">{t('landing.howItWorks.process.studentDesc')}</p>
                  </div>
                </div>
                <ArrowRight className="text-muted-foreground hidden md:block" size={24} />
                <div className="flex items-center gap-4">
                  <div className="flex items-center justify-center h-12 w-12 bg-secondary text-white rounded-lg">
                    <Database size={24} />
                  </div>
                  <div>
                    <h4 className="font-semibold">{t('landing.howItWorks.process.system')}</h4>
                    <p className="text-sm text-muted-foreground">{t('landing.howItWorks.process.systemDesc')}</p>
                  </div>
                </div>
                <ArrowRight className="text-muted-foreground hidden md:block" size={24} />
                <div className="flex items-center gap-4">
                  <div className="flex items-center justify-center h-12 w-12 bg-green-600 text-white rounded-lg">
                    <Users size={24} />
                  </div>
                  <div>
                    <h4 className="font-semibold">{t('landing.howItWorks.process.teachers')}</h4>
                    <p className="text-sm text-muted-foreground">{t('landing.howItWorks.process.teachersDesc')}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* User Types & Benefits */}
        <section className="py-20 bg-gray-50 dark:bg-gray-900/50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <Badge variant="outline" className="mb-4">
                {t('landing.userTypes.badge')}
              </Badge>
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                {t('landing.userTypes.title')}
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                {t('landing.userTypes.subtitle')}
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
              {/* Students */}
              <Card className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
                <CardHeader className="text-center pb-4">
                  <div className="flex items-center justify-center h-16 w-16 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-2xl mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
                    <Users size={32} />
                  </div>
                  <CardTitle className="text-2xl">{t('landing.userTypes.students.title')}</CardTitle>
                  <CardDescription className="text-base">
                    {t('landing.userTypes.students.subtitle')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    <li className="flex items-start gap-3">
                      <CheckCircle size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm leading-relaxed">{t('landing.userTypes.students.feature1')}</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <CheckCircle size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm leading-relaxed">{t('landing.userTypes.students.feature2')}</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <CheckCircle size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm leading-relaxed">{t('landing.userTypes.students.feature3')}</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <CheckCircle size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm leading-relaxed">{t('landing.userTypes.students.feature4')}</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <CheckCircle size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm leading-relaxed">{t('landing.userTypes.students.feature5')}</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              {/* Teachers */}
              <Card className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
                <CardHeader className="text-center pb-4">
                  <div className="flex items-center justify-center h-16 w-16 bg-gradient-to-br from-purple-500 to-purple-600 text-white rounded-2xl mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
                    <BarChart4 size={32} />
                  </div>
                  <CardTitle className="text-2xl">{t('landing.userTypes.teachers.title')}</CardTitle>
                  <CardDescription className="text-base">
                    {t('landing.userTypes.teachers.subtitle')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    <li className="flex items-start gap-3">
                      <CheckCircle size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm leading-relaxed">{t('landing.userTypes.teachers.feature1')}</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <CheckCircle size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm leading-relaxed">{t('landing.userTypes.teachers.feature2')}</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <CheckCircle size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm leading-relaxed">{t('landing.userTypes.teachers.feature3')}</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <CheckCircle size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm leading-relaxed">{t('landing.userTypes.teachers.feature4')}</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <CheckCircle size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm leading-relaxed">{t('landing.userTypes.teachers.feature5')}</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              {/* Administrators */}
              <Card className="group hover:shadow-xl transition-all duration-300 border-0 shadow-lg">
                <CardHeader className="text-center pb-4">
                  <div className="flex items-center justify-center h-16 w-16 bg-gradient-to-br from-teal-500 to-teal-600 text-white rounded-2xl mb-4 mx-auto group-hover:scale-110 transition-transform duration-300">
                    <ShieldCheck size={32} />
                  </div>
                  <CardTitle className="text-2xl">{t('landing.userTypes.administrators.title')}</CardTitle>
                  <CardDescription className="text-base">
                    {t('landing.userTypes.administrators.subtitle')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    <li className="flex items-start gap-3">
                      <CheckCircle size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm leading-relaxed">{t('landing.userTypes.administrators.feature1')}</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <CheckCircle size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm leading-relaxed">{t('landing.userTypes.administrators.feature2')}</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <CheckCircle size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm leading-relaxed">{t('landing.userTypes.administrators.feature3')}</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <CheckCircle size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm leading-relaxed">{t('landing.userTypes.administrators.feature4')}</span>
                    </li>
                    <li className="flex items-start gap-3">
                      <CheckCircle size={20} className="text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm leading-relaxed">{t('landing.userTypes.administrators.feature5')}</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>

            {/* System Admin Features */}
            <div className="bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10 rounded-2xl p-8">
              <div className="text-center mb-8">
                <div className="flex items-center justify-center h-16 w-16 bg-gradient-to-br from-primary to-secondary text-white rounded-2xl mb-4 mx-auto">
                  <Lock size={32} />
                </div>
                <h3 className="text-2xl font-bold mb-2">{t('landing.userTypes.systemAdmin.title')}</h3>
                <p className="text-muted-foreground">{t('landing.userTypes.systemAdmin.subtitle')}</p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="flex items-center justify-center h-12 w-12 bg-primary/20 text-primary rounded-lg mb-3 mx-auto">
                    <Database size={24} />
                  </div>
                  <h4 className="font-semibold mb-1">{t('landing.userTypes.systemAdmin.feature1')}</h4>
                  <p className="text-sm text-muted-foreground">{t('landing.userTypes.systemAdmin.feature1Desc')}</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center h-12 w-12 bg-primary/20 text-primary rounded-lg mb-3 mx-auto">
                    <RefreshCw size={24} />
                  </div>
                  <h4 className="font-semibold mb-1">{t('landing.userTypes.systemAdmin.feature2')}</h4>
                  <p className="text-sm text-muted-foreground">{t('landing.userTypes.systemAdmin.feature2Desc')}</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center h-12 w-12 bg-primary/20 text-primary rounded-lg mb-3 mx-auto">
                    <Shield size={24} />
                  </div>
                  <h4 className="font-semibold mb-1">{t('landing.userTypes.systemAdmin.feature3')}</h4>
                  <p className="text-sm text-muted-foreground">{t('landing.userTypes.systemAdmin.feature3Desc')}</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center h-12 w-12 bg-primary/20 text-primary rounded-lg mb-3 mx-auto">
                    <Settings size={24} />
                  </div>
                  <h4 className="font-semibold mb-1">{t('landing.userTypes.systemAdmin.feature4')}</h4>
                  <p className="text-sm text-muted-foreground">{t('landing.userTypes.systemAdmin.feature4Desc')}</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Final CTA Section */}
        <section className="relative py-20 bg-gradient-to-br from-primary via-primary/90 to-secondary text-white overflow-hidden dark:from-[#F39228] dark:via-[#F39228]/90 dark:to-[#F39228]/80">
          {/* Background Pattern */}
          <div className="absolute inset-0 bg-grid-white/[0.05] bg-[size:60px_60px]" />
          <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent" />

          <div className="container mx-auto px-4 relative z-10">
            <div className="text-center max-w-4xl mx-auto">
              <Badge variant="secondary" className="mb-6 bg-white/10 text-white border-white/20">
                <Zap className="w-4 h-4 mr-2" />
                {t('landing.cta.badge')}
              </Badge>

              <h2 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                {t('landing.cta.title')}
              </h2>

              <p className="text-xl md:text-2xl mb-8 text-white/90 leading-relaxed">
                {t('landing.cta.description')}
              </p>

              {/* Key Benefits */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
                <div className="flex items-center justify-center gap-3 bg-white/10 backdrop-blur-sm rounded-lg p-4">
                  <Clock className="w-6 h-6 text-white" />
                  <span className="font-semibold">{t('landing.cta.benefit1')}</span>
                </div>
                <div className="flex items-center justify-center gap-3 bg-white/10 backdrop-blur-sm rounded-lg p-4">
                  <Shield className="w-6 h-6 text-white" />
                  <span className="font-semibold">{t('landing.cta.benefit2')}</span>
                </div>
                <div className="flex items-center justify-center gap-3 bg-white/10 backdrop-blur-sm rounded-lg p-4">
                  <TrendingUp className="w-6 h-6 text-white" />
                  <span className="font-semibold">{t('landing.cta.benefit3')}</span>
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row justify-center gap-4 mb-12">
                <Button
                  size="lg"
                  className="bg-white hover:bg-gray-100 shadow-lg hover:shadow-xl transition-all duration-300 text-lg px-8 py-6"
                  asChild
                >
                  <Link to="/login" className="text-red-600 hover:text-red-700 flex items-center">
                    {t('landing.cta.startNow')}
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Link>
                </Button>
                <Button
                  size="lg"
                  variant="outline"
                  className="border-2 border-white bg-transparent hover:bg-white/10 backdrop-blur-sm text-lg px-8 py-6"
                  asChild
                >
                  <Link to="/signup" className="text-white hover:text-white flex items-center">
                    {t('landing.cta.createAccount')}
                  </Link>
                </Button>
              </div>

              {/* Trust Indicators */}
              <div className="border-t border-white/20 pt-8">
                <p className="text-white/80 mb-4">{t('landing.cta.trustIndicator')}</p>
                <div className="flex flex-wrap justify-center items-center gap-8 text-white/60">
                  <div className="flex items-center gap-2">
                    <Shield className="w-5 h-5" />
                    <span className="text-sm">{t('landing.cta.secure')}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Wifi className="w-5 h-5" />
                    <span className="text-sm">{t('landing.cta.realtime')}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Globe className="w-5 h-5" />
                    <span className="text-sm">{t('landing.cta.multilingual')}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Cloud className="w-5 h-5" />
                    <span className="text-sm">{t('landing.cta.cloud')}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
