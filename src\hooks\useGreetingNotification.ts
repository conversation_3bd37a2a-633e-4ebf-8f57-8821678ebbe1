import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';

type GreetingType = 'welcomeBack' | 'firstTime' | 'newUser';

interface UseGreetingNotificationReturn {
  showGreeting: boolean;
  greetingType: GreetingType;
  hideGreeting: () => void;
}

export function useGreetingNotification(): UseGreetingNotificationReturn {
  const [showGreeting, setShowGreeting] = useState(false);
  const [greetingType, setGreetingType] = useState<GreetingType>('welcomeBack');
  const { user } = useAuth();

  useEffect(() => {
    if (!user) return;

    // Check if this is a new session (not a page reload)
    const isNewSession = !sessionStorage.getItem('ai_assistant_session');

    // Check if enough time has passed since last greeting (at least 5 minutes for testing)
    const lastGreetingTime = localStorage.getItem('ai_assistant_last_greeting');
    const now = Date.now();
    const fiveMinutes = 5 * 60 * 1000;

    const shouldShowGreeting = isNewSession ||
      !lastGreetingTime ||
      (now - parseInt(lastGreetingTime)) > fiveMinutes;

    if (!shouldShowGreeting) return;

    // Mark this as a new session and update last greeting time
    sessionStorage.setItem('ai_assistant_session', 'true');
    localStorage.setItem('ai_assistant_last_greeting', now.toString());

    // Determine greeting type based on user data
    const determineGreetingType = (): GreetingType => {
      // Check if user was created recently (within last 24 hours)
      const userCreatedAt = new Date(user.created_at);
      const now = new Date();
      const hoursSinceCreation = (now.getTime() - userCreatedAt.getTime()) / (1000 * 60 * 60);

      // Check if user has ever seen the greeting before
      const hasSeenGreeting = localStorage.getItem(`ai_greeting_seen_${user.id}`);

      if (!hasSeenGreeting) {
        // First time ever seeing the AI assistant
        localStorage.setItem(`ai_greeting_seen_${user.id}`, 'true');
        
        if (hoursSinceCreation < 24) {
          return 'newUser'; // New user, first time
        } else {
          return 'firstTime'; // Existing user, first time seeing AI
        }
      } else {
        return 'welcomeBack'; // Returning user
      }
    };

    const type = determineGreetingType();
    setGreetingType(type);
    
    // Show greeting after a short delay
    const timer = setTimeout(() => {
      setShowGreeting(true);
    }, 2000); // Show after 2 seconds

    // Auto-hide after 8 seconds (longer to allow reading the typewriter effect)
    const autoHideTimer = setTimeout(() => {
      setShowGreeting(false);
    }, 10000);

    return () => {
      clearTimeout(timer);
      clearTimeout(autoHideTimer);
    };
  }, [user]);

  const hideGreeting = () => {
    setShowGreeting(false);
  };

  return {
    showGreeting,
    greetingType,
    hideGreeting
  };
}
