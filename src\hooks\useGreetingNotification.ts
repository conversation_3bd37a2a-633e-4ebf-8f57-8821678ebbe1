import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLocation } from 'react-router-dom';

type GreetingType = 'welcomeBack' | 'firstTime' | 'newUser';
type UserRole = 'student' | 'teacher' | 'admin';

interface UseGreetingNotificationReturn {
  showGreeting: boolean;
  greetingType: GreetingType;
  userRole: UserRole;
  hideGreeting: () => void;
}

export function useGreetingNotification(): UseGreetingNotificationReturn {
  const [showGreeting, setShowGreeting] = useState(false);
  const [greetingType, setGreetingType] = useState<GreetingType>('welcomeBack');
  const [userRole, setUserRole] = useState<UserRole>('student');
  const { user } = useAuth();
  const location = useLocation();

  useEffect(() => {
    if (!user) return;

    // Get user role from profile
    const role = (user.role || 'student') as User<PERSON>ole;
    setUserRole(role);

    // Check if this is a new session (not a page reload)
    const isNewSession = !sessionStorage.getItem('ai_assistant_session');

    // Always show greeting on every visit (new session)
    if (!isNewSession) return;

    // Mark this as a new session
    sessionStorage.setItem('ai_assistant_session', 'true');

    // Determine greeting type based on user data and visit history
    const determineGreetingType = (): GreetingType => {
      // Check if user was created recently (within last 7 days for new user greeting)
      const userCreatedAt = user.created_at ? new Date(user.created_at) : new Date();
      const now = new Date();
      const daysSinceCreation = (now.getTime() - userCreatedAt.getTime()) / (1000 * 60 * 60 * 24);

      // Check if user has ever seen the AI assistant greeting before
      const hasSeenAIGreeting = localStorage.getItem(`ai_greeting_seen_${user.id}`);

      if (!hasSeenAIGreeting) {
        // First time ever seeing the AI assistant
        localStorage.setItem(`ai_greeting_seen_${user.id}`, 'true');

        if (daysSinceCreation < 7) {
          return 'newUser'; // New user (within 7 days), first time seeing AI
        } else {
          return 'firstTime'; // Existing user, first time seeing AI
        }
      } else {
        // Check last visit time for contextual greeting
        const lastVisitTime = localStorage.getItem(`last_visit_${user.id}`);
        const currentTime = now.getTime();

        if (lastVisitTime) {
          const hoursSinceLastVisit = (currentTime - parseInt(lastVisitTime)) / (1000 * 60 * 60);

          // If it's been more than 24 hours, show a more welcoming message
          if (hoursSinceLastVisit > 24) {
            return 'firstTime'; // Long time no see
          }
        }

        // Store current visit time
        localStorage.setItem(`last_visit_${user.id}`, currentTime.toString());

        return 'welcomeBack'; // Regular returning user
      }
    };

    const type = determineGreetingType();
    setGreetingType(type);

    // Show greeting after a short delay
    const timer = setTimeout(() => {
      setShowGreeting(true);
    }, 2000); // Show after 2 seconds

    // Auto-hide after 30 seconds as requested
    const autoHideTimer = setTimeout(() => {
      setShowGreeting(false);
    }, 32000); // 30 seconds + 2 seconds initial delay

    return () => {
      clearTimeout(timer);
      clearTimeout(autoHideTimer);
    };
  }, [user]);

  const hideGreeting = () => {
    setShowGreeting(false);
  };

  return {
    showGreeting,
    greetingType,
    userRole,
    hideGreeting
  };
}
