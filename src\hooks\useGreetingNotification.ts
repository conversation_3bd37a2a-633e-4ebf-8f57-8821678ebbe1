import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';

type GreetingType = 'welcomeBack' | 'firstTime' | 'newUser';

interface UseGreetingNotificationReturn {
  showGreeting: boolean;
  greetingType: GreetingType;
  hideGreeting: () => void;
}

export function useGreetingNotification(): UseGreetingNotificationReturn {
  const [showGreeting, setShowGreeting] = useState(false);
  const [greetingType, setGreetingType] = useState<GreetingType>('welcomeBack');
  const { user } = useAuth();

  useEffect(() => {
    if (!user) return;

    // Check if this is a new session (not a page reload)
    const isNewSession = !sessionStorage.getItem('ai_assistant_session');
    
    if (!isNewSession) return;

    // Mark this as a new session
    sessionStorage.setItem('ai_assistant_session', 'true');

    // Determine greeting type based on user data
    const determineGreetingType = (): GreetingType => {
      // Check if user was created recently (within last 24 hours)
      const userCreatedAt = new Date(user.created_at);
      const now = new Date();
      const hoursSinceCreation = (now.getTime() - userCreatedAt.getTime()) / (1000 * 60 * 60);

      // Check if user has ever seen the greeting before
      const hasSeenGreeting = localStorage.getItem(`ai_greeting_seen_${user.id}`);

      if (!hasSeenGreeting) {
        // First time ever seeing the AI assistant
        localStorage.setItem(`ai_greeting_seen_${user.id}`, 'true');
        
        if (hoursSinceCreation < 24) {
          return 'newUser'; // New user, first time
        } else {
          return 'firstTime'; // Existing user, first time seeing AI
        }
      } else {
        return 'welcomeBack'; // Returning user
      }
    };

    const type = determineGreetingType();
    setGreetingType(type);
    
    // Show greeting after a short delay
    const timer = setTimeout(() => {
      setShowGreeting(true);
    }, 1000);

    // Auto-hide after 5 seconds
    const autoHideTimer = setTimeout(() => {
      setShowGreeting(false);
    }, 6000);

    return () => {
      clearTimeout(timer);
      clearTimeout(autoHideTimer);
    };
  }, [user]);

  const hideGreeting = () => {
    setShowGreeting(false);
  };

  return {
    showGreeting,
    greetingType,
    hideGreeting
  };
}
