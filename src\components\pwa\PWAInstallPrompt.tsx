import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Download,
  WifiOff,
  RefreshCw,
  X,
  Clock,
  Sparkles
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { pwaService } from '@/lib/services/pwa-service';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';

export default function PWAInstallPrompt() {
  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isInstalling, setIsInstalling] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [appInfo, setAppInfo] = useState(pwaService.getAppInfo());
  const [hasShownPrompt, setHasShownPrompt] = useState(false);
  const [timeOnPage, setTimeOnPage] = useState(0);
  const { t } = useTranslation();

  useEffect(() => {
    // Check if user has already seen the prompt
    const hasSeenPrompt = localStorage.getItem('pwa-install-prompt-shown') === 'true';
    const remindLater = localStorage.getItem('pwa-remind-later');

    if (hasSeenPrompt || appInfo.isInstalled) {
      return;
    }

    // Check if user chose "remind later" and if enough time has passed (24 hours)
    if (remindLater) {
      const remindTime = parseInt(remindLater);
      const now = Date.now();
      if (now - remindTime < 24 * 60 * 60 * 1000) { // 24 hours
        return;
      }
    }

    // Timer to show prompt after 10 seconds
    const timer = setInterval(() => {
      setTimeOnPage(prev => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [appInfo.isInstalled]);

  useEffect(() => {
    // Show prompt after 10 seconds if conditions are met
    if (timeOnPage >= 10 && !hasShownPrompt && !appInfo.isInstalled && pwaService.isInstallPromptAvailable()) {
      setShowInstallPrompt(true);
      setHasShownPrompt(true);
    }
  }, [timeOnPage, hasShownPrompt, appInfo.isInstalled]);

  useEffect(() => {
    // Listen for PWA events
    const handleInstallCompleted = () => {
      setShowInstallPrompt(false);
      localStorage.setItem('pwa-install-prompt-shown', 'true');
      toast.success(t('pwa.installSuccess'), {
        description: t('pwa.installSuccessDescription'),
      });
    };

    const handleUpdateAvailable = () => {
      setShowUpdatePrompt(true);
    };

    const handleOnlineStatus = (event: CustomEvent) => {
      setIsOnline(event.detail.isOnline);
    };

    const handleManualInstall = (event: CustomEvent) => {
      console.log('Manual install instructions:', event.detail.instructions);
      toast.info(t('pwa.manualInstall'), {
        description: event.detail.instructions,
        duration: 8000,
      });
    };

    // Add event listeners
    window.addEventListener('pwa-install-completed', handleInstallCompleted);
    window.addEventListener('pwa-update-available', handleUpdateAvailable);
    window.addEventListener('pwa-online-status', handleOnlineStatus as EventListener);
    window.addEventListener('pwa-manual-install', handleManualInstall as EventListener);

    return () => {
      window.removeEventListener('pwa-install-completed', handleInstallCompleted);
      window.removeEventListener('pwa-update-available', handleUpdateAvailable);
      window.removeEventListener('pwa-online-status', handleOnlineStatus as EventListener);
      window.removeEventListener('pwa-manual-install', handleManualInstall as EventListener);
    };
  }, [t]);

  const handleInstall = async () => {
    console.log('handleInstall called - starting installation process');

    // Debug PWA service state
    pwaService.debugState();

    setIsInstalling(true);
    try {
      console.log('Install button clicked');
      const success = await pwaService.showInstallPrompt();
      console.log('Install prompt result:', success);
      if (success) {
        console.log('Install successful');
        setShowInstallPrompt(false);
        localStorage.setItem('pwa-install-prompt-shown', 'true');
        toast.success(t('pwa.installSuccess'), {
          description: t('pwa.installSuccessDescription'),
        });
      } else {
        console.log('Install was not successful or user dismissed');
        // Don't hide the prompt if install failed, user might try again
      }
    } catch (error) {
      console.error('Install failed:', error);
      toast.error(t('pwa.installFailed'));
    } finally {
      console.log('Install process finished, setting isInstalling to false');
      setIsInstalling(false);
    }
  };

  const handleRemindLater = () => {
    console.log('handleRemindLater called - hiding prompt and setting reminder');
    setShowInstallPrompt(false);
    localStorage.setItem('pwa-remind-later', Date.now().toString());
    // Remove the permanent dismissal flag so prompt can show again later
    localStorage.removeItem('pwa-install-prompt-shown');
    console.log('Remind later storage set, showing toast');
    toast.info(t('pwa.remindLater'), {
      description: t('pwa.remindLaterDescription'),
    });
  };

  const handleClose = () => {
    setShowInstallPrompt(false);
    localStorage.setItem('pwa-install-prompt-shown', 'true');
  };

  const handleUpdate = async () => {
    setIsUpdating(true);
    try {
      await pwaService.applyUpdate();
      toast.success(t('pwa.updateApplied'));
    } catch (error) {
      console.error('Update failed:', error);
      toast.error(t('pwa.updateFailed'));
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <>
      {/* Online/Offline Status Indicator */}
      <AnimatePresence>
        {!isOnline && (
          <motion.div
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 100 }}
            className="fixed top-4 right-4 z-50"
          >
            <Badge variant="destructive" className="flex items-center gap-2 px-3 py-2">
              <WifiOff className="w-4 h-4" />
              {t('pwa.offline')}
            </Badge>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Compact Install Prompt */}
      <AnimatePresence>
        {showInstallPrompt && !appInfo.isInstalled && (
          <motion.div
            initial={{ opacity: 0, x: 100, scale: 0.9 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 100, scale: 0.9 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
            className="fixed top-4 right-4 z-50 w-80"
          >
            <div className="relative bg-gradient-to-br from-[#08194A] via-[#0A1B4D] to-[#0C1E50] text-white rounded-xl shadow-2xl border border-[#EE0D09]/20 overflow-hidden" style={{ pointerEvents: 'auto' }}>
              {/* Sparkle animation background */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse pointer-events-none"></div>

              {/* Close button */}
              <button
                onClick={handleClose}
                className="absolute top-3 right-3 w-6 h-6 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-colors"
              >
                <X className="w-4 h-4" />
              </button>

              <div className="p-4">
                {/* Header with icon */}
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-[#EE0D09] to-[#FF1A1A] flex items-center justify-center">
                    <Sparkles className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-sm">{t('pwa.installTitle')}</h3>
                    <p className="text-xs text-gray-300">{t('pwa.installCompactDescription')}</p>
                  </div>
                </div>

                {/* Action buttons */}
                <div className="flex gap-2 relative z-10">
                  <Button
                    onClick={() => {
                      console.log('🔴 INSTALL BUTTON CLICKED - This should appear in console');
                      alert('Install button clicked!'); // Temporary alert for testing
                      handleInstall();
                    }}
                    onMouseDown={() => console.log('🔴 Install button mouse down')}
                    onMouseUp={() => console.log('🔴 Install button mouse up')}
                    disabled={isInstalling}
                    size="sm"
                    className="flex-1 bg-gradient-to-r from-[#EE0D09] to-[#FF1A1A] hover:from-[#D00B08] hover:to-[#E61717] text-white border-0 h-8 text-xs font-medium cursor-pointer"
                    style={{ pointerEvents: 'auto' }}
                  >
                    {isInstalling ? (
                      <>
                        <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                        {t('pwa.installing')}
                      </>
                    ) : (
                      <>
                        <Download className="w-3 h-3 mr-1" />
                        {t('pwa.install')}
                      </>
                    )}
                  </Button>

                  <Button
                    onClick={() => {
                      console.log('🟡 REMIND LATER BUTTON CLICKED - This should appear in console');
                      alert('Remind later button clicked!'); // Temporary alert for testing
                      handleRemindLater();
                    }}
                    onMouseDown={() => console.log('🟡 Remind later button mouse down')}
                    onMouseUp={() => console.log('🟡 Remind later button mouse up')}
                    variant="ghost"
                    size="sm"
                    className="flex-1 text-gray-300 hover:text-white hover:bg-white/10 h-8 text-xs cursor-pointer"
                    style={{ pointerEvents: 'auto' }}
                  >
                    <Clock className="w-3 h-3 mr-1" />
                    {t('pwa.later')}
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Compact Update Prompt */}
      <AnimatePresence>
        {showUpdatePrompt && (
          <motion.div
            initial={{ opacity: 0, x: 100, scale: 0.9 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 100, scale: 0.9 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
            className="fixed top-20 right-4 z-50 w-80"
          >
            <div className="relative bg-gradient-to-br from-green-600 via-green-700 to-green-800 text-white rounded-xl shadow-2xl border border-green-400/20 overflow-hidden">
              {/* Sparkle animation background */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse"></div>

              {/* Close button */}
              <button
                onClick={() => setShowUpdatePrompt(false)}
                className="absolute top-3 right-3 w-6 h-6 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-colors"
              >
                <X className="w-4 h-4" />
              </button>

              <div className="p-4">
                {/* Header with icon */}
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-emerald-500 to-green-600 flex items-center justify-center">
                    <RefreshCw className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-sm">{t('pwa.updateTitle')}</h3>
                    <p className="text-xs text-green-100">{t('pwa.updateCompactDescription')}</p>
                  </div>
                </div>

                {/* Action buttons */}
                <div className="flex gap-2">
                  <Button
                    onClick={handleUpdate}
                    disabled={isUpdating}
                    size="sm"
                    className="flex-1 bg-white text-green-700 hover:bg-green-50 border-0 h-8 text-xs font-medium"
                  >
                    {isUpdating ? (
                      <>
                        <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                        {t('pwa.updating')}
                      </>
                    ) : (
                      <>
                        <RefreshCw className="w-3 h-3 mr-1" />
                        {t('pwa.update')}
                      </>
                    )}
                  </Button>

                  <Button
                    onClick={() => setShowUpdatePrompt(false)}
                    variant="ghost"
                    size="sm"
                    className="flex-1 text-green-100 hover:text-white hover:bg-white/10 h-8 text-xs"
                  >
                    {t('pwa.later')}
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
