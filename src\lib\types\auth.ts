/**
 * Authentication and User Types
 * Centralized type definitions for user authentication and profiles
 */

export type UserRole = "student" | "teacher" | "admin" | "system_admin";
export type Language = "en" | "tr";
export type UserStatus = "active" | "inactive" | "blocked" | "deleted";

export interface BaseUser {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  created_at?: string;
  updated_at?: string;
  photoUrl?: string;
  status?: UserStatus;
  last_login_at?: string;
  user_id?: string;
  school_id?: string;
  school?: string; // School name for backward compatibility
  invitationCode?: string; // Used during signup for school validation
  preferred_language?: Language;
}

export interface UserAccountStatus {
  is_deleted?: boolean;
  deleted_at?: string;
  is_blocked?: boolean;
  blocked_at?: string;
  maintenance_mode?: boolean;
  profile_completed?: boolean;
}

export interface StudentProfile extends BaseUser {
  role: "student";
  studentId?: string;
  course?: string;
  block_id?: string;
  room_id?: string;
  blockName?: string;
  roomNumber?: string;
  biometricRegistered?: boolean;
  pin?: string;
}

export interface TeacherProfile extends BaseUser {
  role: "teacher";
  teacherId?: string;
  department?: string;
  subject?: string;
  position?: string;
}

export interface AdminProfile extends BaseUser {
  role: "admin";
  adminId?: string;
  accessLevel?: number; // 1: School admin, 2: District admin, 3: System admin
  position?: string;
  profileCompleted?: boolean;
}

export interface SystemAdminProfile extends BaseUser {
  role: "system_admin";
  accessLevel: number;
  permissions: string[];
}

export type User = BaseUser & UserAccountStatus & (
  | StudentProfile 
  | TeacherProfile 
  | AdminProfile 
  | SystemAdminProfile
);

export interface Student extends StudentProfile {
  blocks?: {
    id: string;
    name: string;
  };
  rooms?: {
    id: string;
    name: string;
    block_id: string;
  };
}

export interface Teacher extends TeacherProfile {}

export interface Admin extends AdminProfile {}

export interface AuthSession {
  user: User;
  access_token: string;
  refresh_token?: string;
  expires_at?: number;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface SignUpData extends LoginCredentials {
  name: string;
  role: UserRole;
  invitationCode?: string;
  school_id?: string;
}

export interface BiometricCredentials {
  id: string;
  rawId: ArrayBuffer;
  response: {
    clientDataJSON: ArrayBuffer;
    authenticatorData: ArrayBuffer;
    signature: ArrayBuffer;
    userHandle?: ArrayBuffer;
  };
  type: "public-key";
}
