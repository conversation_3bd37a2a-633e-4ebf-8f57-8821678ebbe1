import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Smartphone, 
  Download, 
  CheckCircle, 
  XCircle, 
  Wifi, 
  WifiOff,
  RefreshCw,
  Bell,
  Zap
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { pwaService } from '@/lib/services/pwa-service';

export default function PWAStatus() {
  const [appInfo, setAppInfo] = useState(pwaService.getAppInfo());
  const [isInstalling, setIsInstalling] = useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    const interval = setInterval(() => {
      setAppInfo(pwaService.getAppInfo());
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const handleInstall = async () => {
    setIsInstalling(true);
    try {
      await pwaService.showInstallPrompt();
    } catch (error) {
      console.error('Install failed:', error);
    } finally {
      setIsInstalling(false);
    }
  };

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <XCircle className="w-4 h-4 text-red-500" />
    );
  };

  const getStatusBadge = (status: boolean, trueText: string, falseText: string) => {
    return (
      <Badge variant={status ? "default" : "secondary"} className={status ? "bg-green-600" : ""}>
        {status ? trueText : falseText}
      </Badge>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Smartphone className="w-5 h-5" />
          PWA Status
        </CardTitle>
        <CardDescription>
          Progressive Web App installation and feature status
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Installation Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-3">
            {getStatusIcon(appInfo.isInstalled)}
            <div>
              <p className="font-medium">App Installation</p>
              <p className="text-sm text-gray-600">
                {appInfo.isInstalled ? 'Installed on device' : 'Not installed'}
              </p>
            </div>
          </div>
          {getStatusBadge(appInfo.isInstalled, 'Installed', 'Not Installed')}
        </div>

        {/* Service Worker Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-3">
            {getStatusIcon(appInfo.serviceWorkerRegistered)}
            <div>
              <p className="font-medium">Service Worker</p>
              <p className="text-sm text-gray-600">
                {appInfo.serviceWorkerRegistered ? 'Active and running' : 'Not registered'}
              </p>
            </div>
          </div>
          {getStatusBadge(appInfo.serviceWorkerRegistered, 'Active', 'Inactive')}
        </div>

        {/* Online Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-3">
            {appInfo.isOnline ? (
              <Wifi className="w-4 h-4 text-green-500" />
            ) : (
              <WifiOff className="w-4 h-4 text-red-500" />
            )}
            <div>
              <p className="font-medium">Network Status</p>
              <p className="text-sm text-gray-600">
                {appInfo.isOnline ? 'Connected to internet' : 'Offline mode'}
              </p>
            </div>
          </div>
          {getStatusBadge(appInfo.isOnline, 'Online', 'Offline')}
        </div>

        {/* Update Status */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-3">
            {getStatusIcon(!appInfo.updateAvailable)}
            <div>
              <p className="font-medium">App Version</p>
              <p className="text-sm text-gray-600">
                {appInfo.updateAvailable ? 'Update available' : 'Up to date'}
              </p>
            </div>
          </div>
          {getStatusBadge(!appInfo.updateAvailable, 'Latest', 'Update Available')}
        </div>

        {/* PWA Features */}
        <div className="space-y-3">
          <h3 className="font-medium text-gray-900">PWA Features</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div className="flex items-center gap-2 p-2 bg-blue-50 rounded">
              <Zap className="w-4 h-4 text-blue-600" />
              <span className="text-sm">Offline Access</span>
            </div>
            
            <div className="flex items-center gap-2 p-2 bg-green-50 rounded">
              <Bell className="w-4 h-4 text-green-600" />
              <span className="text-sm">Push Notifications</span>
            </div>
            
            <div className="flex items-center gap-2 p-2 bg-purple-50 rounded">
              <Download className="w-4 h-4 text-purple-600" />
              <span className="text-sm">App-like Experience</span>
            </div>
            
            <div className="flex items-center gap-2 p-2 bg-orange-50 rounded">
              <RefreshCw className="w-4 h-4 text-orange-600" />
              <span className="text-sm">Auto Updates</span>
            </div>
          </div>
        </div>

        {/* Install Button */}
        {!appInfo.isInstalled && appInfo.installPromptAvailable && (
          <Button 
            onClick={handleInstall}
            disabled={isInstalling}
            className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
          >
            {isInstalling ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Installing...
              </>
            ) : (
              <>
                <Download className="w-4 h-4 mr-2" />
                Install App
              </>
            )}
          </Button>
        )}

        {/* Installation Instructions */}
        {!appInfo.isInstalled && !appInfo.installPromptAvailable && (
          <div className="p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800 font-medium mb-2">Manual Installation:</p>
            <ul className="text-xs text-blue-700 space-y-1">
              <li>• Chrome/Edge: Click menu → "Install app"</li>
              <li>• Safari: Share → "Add to Home Screen"</li>
              <li>• Firefox: Menu → "Install"</li>
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
