/**
 * PWA Service - Handles Progressive Web App functionality
 * Including install prompts, offline detection, and app updates
 */

export interface PWAInstallPrompt {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

class PWAService {
  private deferredPrompt: P<PERSON>InstallPrompt | null = null;
  private isInstalled = false;
  private isOnline = navigator.onLine;
  private updateAvailable = false;
  private registration: ServiceWorkerRegistration | null = null;

  constructor() {
    this.initializePWA();
  }

  /**
   * Initialize PWA functionality
   */
  private async initializePWA(): Promise<void> {
    // Register service worker
    await this.registerServiceWorker();
    
    // Set up install prompt handling
    this.setupInstallPrompt();
    
    // Set up online/offline detection
    this.setupOnlineDetection();
    
    // Check if app is already installed
    this.checkInstallStatus();
    
    // Set up update detection
    this.setupUpdateDetection();
  }

  /**
   * Register service worker
   */
  private async registerServiceWorker(): Promise<void> {
    if ('serviceWorker' in navigator) {
      try {
        this.registration = await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker registered successfully:', this.registration);
        
        // Listen for updates
        this.registration.addEventListener('updatefound', () => {
          const newWorker = this.registration?.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                this.updateAvailable = true;
                this.notifyUpdateAvailable();
              }
            });
          }
        });

        // Listen for controller changes
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          console.log('Service worker controller changed');
          // Reload the page when a new service worker takes control
          if (this.updateAvailable) {
            window.location.reload();
          }
        });
      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    }
  }

  /**
   * Set up install prompt handling
   */
  private setupInstallPrompt(): void {
    window.addEventListener('beforeinstallprompt', (e) => {
      console.log('PWA install prompt available');
      e.preventDefault();
      this.deferredPrompt = e as any;
      this.showInstallButton();
    });

    window.addEventListener('appinstalled', () => {
      console.log('PWA was installed');
      this.isInstalled = true;
      this.hideInstallButton();
      this.deferredPrompt = null;
    });
  }

  /**
   * Set up online/offline detection
   */
  private setupOnlineDetection(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.notifyOnlineStatus(true);
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.notifyOnlineStatus(false);
    });
  }

  /**
   * Check if app is already installed
   */
  private checkInstallStatus(): void {
    // Check if running in standalone mode (installed)
    if (window.matchMedia('(display-mode: standalone)').matches) {
      this.isInstalled = true;
    }
    
    // Check if running as PWA on iOS
    if ((window.navigator as any).standalone === true) {
      this.isInstalled = true;
    }
  }

  /**
   * Set up update detection
   */
  private setupUpdateDetection(): void {
    // Update detection is now handled in registerServiceWorker method
    // to avoid duplicate event listeners
  }

  /**
   * Show install prompt
   */
  async showInstallPrompt(): Promise<boolean> {
    if (!this.deferredPrompt) {
      console.log('No install prompt available');
      return false;
    }

    try {
      await this.deferredPrompt.prompt();
      const choiceResult = await this.deferredPrompt.userChoice;
      
      if (choiceResult.outcome === 'accepted') {
        console.log('User accepted the install prompt');
        this.deferredPrompt = null;
        return true;
      } else {
        console.log('User dismissed the install prompt');
        return false;
      }
    } catch (error) {
      console.error('Error showing install prompt:', error);
      return false;
    }
  }

  /**
   * Check if install prompt is available
   */
  isInstallPromptAvailable(): boolean {
    return this.deferredPrompt !== null;
  }

  /**
   * Check if app is installed
   */
  isAppInstalled(): boolean {
    return this.isInstalled;
  }

  /**
   * Check if app is online
   */
  isAppOnline(): boolean {
    return this.isOnline;
  }

  /**
   * Check if update is available
   */
  isUpdateAvailable(): boolean {
    return this.updateAvailable;
  }

  /**
   * Apply available update
   */
  async applyUpdate(): Promise<void> {
    try {
      if (this.registration?.waiting) {
        console.log('Applying service worker update...');
        this.registration.waiting.postMessage({ type: 'SKIP_WAITING' });

        // Wait a bit for the new service worker to take control
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else {
        console.log('No waiting service worker found');
      }
    } catch (error) {
      console.error('Failed to apply update:', error);
      throw error;
    }
  }

  /**
   * Show install button (to be implemented by UI)
   */
  private showInstallButton(): void {
    const event = new CustomEvent('pwa-install-available');
    window.dispatchEvent(event);
  }

  /**
   * Hide install button (to be implemented by UI)
   */
  private hideInstallButton(): void {
    const event = new CustomEvent('pwa-install-completed');
    window.dispatchEvent(event);
  }

  /**
   * Notify about online status change
   */
  private notifyOnlineStatus(isOnline: boolean): void {
    const event = new CustomEvent('pwa-online-status', { 
      detail: { isOnline } 
    });
    window.dispatchEvent(event);
  }

  /**
   * Notify about available update
   */
  private notifyUpdateAvailable(): void {
    const event = new CustomEvent('pwa-update-available');
    window.dispatchEvent(event);
  }

  /**
   * Get app info
   */
  getAppInfo() {
    return {
      isInstalled: this.isInstalled,
      isOnline: this.isOnline,
      updateAvailable: this.updateAvailable,
      installPromptAvailable: this.deferredPrompt !== null,
      serviceWorkerRegistered: this.registration !== null
    };
  }
}

// Create singleton instance
export const pwaService = new PWAService();
