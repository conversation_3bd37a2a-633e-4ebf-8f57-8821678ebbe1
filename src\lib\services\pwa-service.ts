/**
 * PWA Service - Handles Progressive Web App functionality
 * Including install prompts, offline detection, and app updates
 */

export interface PWAInstallPrompt {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

class PWAService {
  private deferredPrompt: P<PERSON>InstallPrompt | null = null;
  private isInstalled = false;
  private isOnline = navigator.onLine;
  private updateAvailable = false;
  private registration: ServiceWorkerRegistration | null = null;

  constructor() {
    this.initializePWA();
  }

  /**
   * Initialize PWA functionality
   */
  private async initializePWA(): Promise<void> {
    // Register service worker
    await this.registerServiceWorker();
    
    // Set up install prompt handling
    this.setupInstallPrompt();
    
    // Set up online/offline detection
    this.setupOnlineDetection();
    
    // Check if app is already installed
    this.checkInstallStatus();
    
    // Set up update detection
    this.setupUpdateDetection();
  }

  /**
   * Register service worker
   */
  private async registerServiceWorker(): Promise<void> {
    if ('serviceWorker' in navigator) {
      try {
        this.registration = await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker registered successfully:', this.registration);
        
        // Listen for updates
        this.registration.addEventListener('updatefound', () => {
          const newWorker = this.registration?.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                this.updateAvailable = true;
                this.notifyUpdateAvailable();
              }
            });
          }
        });

        // Listen for controller changes
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          console.log('Service worker controller changed');
          // Reload the page when a new service worker takes control
          if (this.updateAvailable) {
            window.location.reload();
          }
        });
      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    }
  }

  /**
   * Set up install prompt handling
   */
  private setupInstallPrompt(): void {
    console.log('Setting up PWA install prompt listeners...');

    window.addEventListener('beforeinstallprompt', (e) => {
      console.log('PWA beforeinstallprompt event fired - install prompt is available');
      e.preventDefault();
      this.deferredPrompt = e as any;
      console.log('Deferred prompt stored:', !!this.deferredPrompt);
      this.showInstallButton();
    });

    window.addEventListener('appinstalled', () => {
      console.log('PWA appinstalled event fired - app was successfully installed');
      this.isInstalled = true;
      this.hideInstallButton();
      this.deferredPrompt = null;
      // Also store in localStorage for persistence
      localStorage.setItem('pwa-manually-installed', 'true');
    });

    // Check if beforeinstallprompt is supported
    if (!('BeforeInstallPromptEvent' in window)) {
      console.log('beforeinstallprompt not supported in this browser');
    }
  }

  /**
   * Set up online/offline detection
   */
  private setupOnlineDetection(): void {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.notifyOnlineStatus(true);
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      this.notifyOnlineStatus(false);
    });
  }

  /**
   * Check if app is already installed
   */
  private checkInstallStatus(): void {
    // Check if running in standalone mode (installed)
    if (window.matchMedia('(display-mode: standalone)').matches) {
      console.log('App detected as installed (standalone mode)');
      this.isInstalled = true;
      return;
    }

    // Check if running as PWA on iOS
    if ((window.navigator as any).standalone === true) {
      console.log('App detected as installed (iOS standalone)');
      this.isInstalled = true;
      return;
    }

    // Check if running in fullscreen mode (some mobile browsers)
    if (window.matchMedia('(display-mode: fullscreen)').matches) {
      console.log('App detected as installed (fullscreen mode)');
      this.isInstalled = true;
      return;
    }

    // Check localStorage for manual install tracking
    const manuallyInstalled = localStorage.getItem('pwa-manually-installed') === 'true';
    if (manuallyInstalled) {
      console.log('App marked as manually installed');
      this.isInstalled = true;
      return;
    }

    console.log('App not detected as installed');
    this.isInstalled = false;
  }

  /**
   * Set up update detection
   */
  private setupUpdateDetection(): void {
    // Update detection is now handled in registerServiceWorker method
    // to avoid duplicate event listeners
  }

  /**
   * Show install prompt
   */
  async showInstallPrompt(): Promise<boolean> {
    console.log('showInstallPrompt called - deferredPrompt:', !!this.deferredPrompt, 'isInstalled:', this.isInstalled);

    if (!this.deferredPrompt) {
      console.log('No install prompt available - checking if app is already installed or prompt not supported');

      // Check if app is already installed
      if (this.isInstalled) {
        console.log('App is already installed');
        return false;
      }

      // For browsers that don't support beforeinstallprompt, show manual instructions
      console.log('Showing manual install instructions');
      this.showManualInstallInstructions();
      return false;
    }

    try {
      console.log('Showing PWA install prompt...');
      await this.deferredPrompt.prompt();
      const choiceResult = await this.deferredPrompt.userChoice;

      if (choiceResult.outcome === 'accepted') {
        console.log('User accepted the install prompt');
        this.isInstalled = true;
        this.deferredPrompt = null;
        this.hideInstallButton();
        return true;
      } else {
        console.log('User dismissed the install prompt');
        this.deferredPrompt = null; // Clear the prompt after dismissal
        return false;
      }
    } catch (error) {
      console.error('Error showing install prompt:', error);
      this.deferredPrompt = null; // Clear the prompt on error
      return false;
    }
  }

  /**
   * Check if install prompt is available
   */
  isInstallPromptAvailable(): boolean {
    // Always return true if app is not installed to show the prompt
    // The actual install method will handle different scenarios
    return !this.isInstalled;
  }

  /**
   * Show manual install instructions for browsers that don't support beforeinstallprompt
   */
  private showManualInstallInstructions(): void {
    const userAgent = navigator.userAgent.toLowerCase();
    let instructions = '';

    if (userAgent.includes('chrome') || userAgent.includes('edge')) {
      instructions = 'Click the install icon in the address bar or go to Chrome menu > Install app';
    } else if (userAgent.includes('firefox')) {
      instructions = 'Add this site to your home screen through the browser menu';
    } else if (userAgent.includes('safari')) {
      instructions = 'Tap the Share button and select "Add to Home Screen"';
    } else {
      instructions = 'Add this site to your home screen through your browser menu';
    }

    // Dispatch event with manual instructions
    const event = new CustomEvent('pwa-manual-install', {
      detail: { instructions }
    });
    window.dispatchEvent(event);
  }

  /**
   * Check if app is installed
   */
  isAppInstalled(): boolean {
    return this.isInstalled;
  }

  /**
   * Check if app is online
   */
  isAppOnline(): boolean {
    return this.isOnline;
  }

  /**
   * Check if update is available
   */
  isUpdateAvailable(): boolean {
    return this.updateAvailable;
  }

  /**
   * Apply available update
   */
  async applyUpdate(): Promise<void> {
    try {
      if (this.registration?.waiting) {
        console.log('Applying service worker update...');
        this.registration.waiting.postMessage({ type: 'SKIP_WAITING' });

        // Wait a bit for the new service worker to take control
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else {
        console.log('No waiting service worker found');
      }
    } catch (error) {
      console.error('Failed to apply update:', error);
      throw error;
    }
  }

  /**
   * Show install button (to be implemented by UI)
   */
  private showInstallButton(): void {
    const event = new CustomEvent('pwa-install-available');
    window.dispatchEvent(event);
  }

  /**
   * Hide install button (to be implemented by UI)
   */
  private hideInstallButton(): void {
    const event = new CustomEvent('pwa-install-completed');
    window.dispatchEvent(event);
  }

  /**
   * Notify about online status change
   */
  private notifyOnlineStatus(isOnline: boolean): void {
    const event = new CustomEvent('pwa-online-status', { 
      detail: { isOnline } 
    });
    window.dispatchEvent(event);
  }

  /**
   * Notify about available update
   */
  private notifyUpdateAvailable(): void {
    const event = new CustomEvent('pwa-update-available');
    window.dispatchEvent(event);
  }

  /**
   * Get app info
   */
  getAppInfo() {
    const info = {
      isInstalled: this.isInstalled,
      isOnline: this.isOnline,
      updateAvailable: this.updateAvailable,
      installPromptAvailable: this.deferredPrompt !== null,
      serviceWorkerRegistered: this.registration !== null
    };
    console.log('PWA Service getAppInfo:', info);
    return info;
  }

  /**
   * Debug method to check PWA state
   */
  debugState() {
    console.log('PWA Service Debug State:', {
      deferredPrompt: !!this.deferredPrompt,
      isInstalled: this.isInstalled,
      isOnline: this.isOnline,
      updateAvailable: this.updateAvailable,
      registration: !!this.registration,
      userAgent: navigator.userAgent
    });
  }
}

// Create singleton instance
export const pwaService = new PWAService();
