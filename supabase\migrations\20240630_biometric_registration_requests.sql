-- Create biometric registration requests table
CREATE TABLE IF NOT EXISTS biometric_registration_requests (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  school_id UUID NOT NULL REFERENCES schools(id) ON DELETE CASCADE,
  student_name VARCHAR(255) NOT NULL,
  student_email VARCHAR(255) NOT NULL,
  student_number VARCHAR(50),
  block_name VARCHAR(255),
  room_name VARCHAR(255),
  request_reason TEXT,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  admin_notes TEXT,
  approved_by UUID REFERENCES auth.users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  rejected_by UUID REFERENCES auth.users(id),
  rejected_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_biometric_requests_school_id ON biometric_registration_requests(school_id);
CREATE INDEX IF NOT EXISTS idx_biometric_requests_user_id ON biometric_registration_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_biometric_requests_status ON biometric_registration_requests(status);
CREATE INDEX IF NOT EXISTS idx_biometric_requests_created_at ON biometric_registration_requests(created_at);

-- Enable RLS
ALTER TABLE biometric_registration_requests ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Students can only see their own requests
CREATE POLICY "Students can view own biometric requests" ON biometric_registration_requests
  FOR SELECT USING (auth.uid() = user_id);

-- Students can create their own requests
CREATE POLICY "Students can create biometric requests" ON biometric_registration_requests
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Students can update their own pending requests (to add more info)
CREATE POLICY "Students can update own pending requests" ON biometric_registration_requests
  FOR UPDATE USING (auth.uid() = user_id AND status = 'pending');

-- School admins can view all requests for their school
CREATE POLICY "School admins can view school biometric requests" ON biometric_registration_requests
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.user_id = auth.uid() 
      AND profiles.role = 'school_admin' 
      AND profiles.school_id = biometric_registration_requests.school_id
    )
  );

-- School admins can update requests for their school (approve/reject)
CREATE POLICY "School admins can update school biometric requests" ON biometric_registration_requests
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.user_id = auth.uid() 
      AND profiles.role = 'school_admin' 
      AND profiles.school_id = biometric_registration_requests.school_id
    )
  );

-- System admins can view all requests
CREATE POLICY "System admins can view all biometric requests" ON biometric_registration_requests
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.user_id = auth.uid() 
      AND profiles.role = 'system_admin'
    )
  );

-- Create function to automatically update updated_at
CREATE OR REPLACE FUNCTION update_biometric_requests_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER update_biometric_requests_updated_at
  BEFORE UPDATE ON biometric_registration_requests
  FOR EACH ROW
  EXECUTE FUNCTION update_biometric_requests_updated_at();

-- Create function to handle request approval
CREATE OR REPLACE FUNCTION approve_biometric_request(request_id UUID, admin_notes_param TEXT DEFAULT NULL)
RETURNS BOOLEAN AS $$
DECLARE
  request_record biometric_registration_requests%ROWTYPE;
  admin_profile profiles%ROWTYPE;
BEGIN
  -- Get the request
  SELECT * INTO request_record FROM biometric_registration_requests WHERE id = request_id;
  
  -- Get admin profile
  SELECT * INTO admin_profile FROM profiles WHERE user_id = auth.uid();
  
  -- Check if admin belongs to the same school
  IF admin_profile.school_id != request_record.school_id THEN
    RAISE EXCEPTION 'Admin can only approve requests from their own school';
  END IF;
  
  -- Update the request
  UPDATE biometric_registration_requests 
  SET 
    status = 'approved',
    admin_notes = admin_notes_param,
    approved_by = auth.uid(),
    approved_at = NOW(),
    updated_at = NOW()
  WHERE id = request_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to handle request rejection
CREATE OR REPLACE FUNCTION reject_biometric_request(request_id UUID, admin_notes_param TEXT DEFAULT NULL)
RETURNS BOOLEAN AS $$
DECLARE
  request_record biometric_registration_requests%ROWTYPE;
  admin_profile profiles%ROWTYPE;
BEGIN
  -- Get the request
  SELECT * INTO request_record FROM biometric_registration_requests WHERE id = request_id;
  
  -- Get admin profile
  SELECT * INTO admin_profile FROM profiles WHERE user_id = auth.uid();
  
  -- Check if admin belongs to the same school
  IF admin_profile.school_id != request_record.school_id THEN
    RAISE EXCEPTION 'Admin can only reject requests from their own school';
  END IF;
  
  -- Update the request
  UPDATE biometric_registration_requests 
  SET 
    status = 'rejected',
    admin_notes = admin_notes_param,
    rejected_by = auth.uid(),
    rejected_at = NOW(),
    updated_at = NOW()
  WHERE id = request_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
