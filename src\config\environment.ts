/**
 * Environment Configuration
 * Centralized environment variable management with type safety
 */

interface EnvironmentConfig {
  // Application
  NODE_ENV: 'development' | 'production' | 'test';
  APP_NAME: string;
  APP_SHORT_NAME: string;
  APP_DESCRIPTION: string;
  APP_VERSION: string;
  
  // Supabase
  SUPABASE_URL: string;
  SUPABASE_ANON_KEY: string;
  
  // Optional Features
  DEV_MODE: boolean;
  SHOW_DEBUG_INFO: boolean;
  
  // Branding
  CUSTOM_LOGO_URL?: string;
  CUSTOM_PRIMARY_COLOR?: string;
  CUSTOM_SECONDARY_COLOR?: string;
  
  // API URLs
  API_BASE_URL: string;
  WEBSOCKET_URL: string;
  
  // External Services
  SENDGRID_API_KEY?: string;
  TWILIO_ACCOUNT_SID?: string;
  TWILIO_AUTH_TOKEN?: string;
  
  // Security
  JWT_SECRET?: string;
  ENCRYPTION_KEY?: string;
  
  // Performance
  CACHE_TTL: number;
  MAX_CONCURRENT_REQUESTS: number;
  REQUEST_TIMEOUT: number;
}

// Helper function to get environment variable with fallback
function getEnvVar(key: string, fallback?: string): string {
  const value = import.meta.env[`VITE_${key}`] || process.env[key];
  if (!value && !fallback) {
    console.warn(`Environment variable ${key} is not set`);
    return '';
  }
  return value || fallback || '';
}

// Helper function to get boolean environment variable
function getBooleanEnvVar(key: string, fallback: boolean = false): boolean {
  const value = getEnvVar(key);
  if (!value) return fallback;
  return value.toLowerCase() === 'true' || value === '1';
}

// Helper function to get number environment variable
function getNumberEnvVar(key: string, fallback: number): number {
  const value = getEnvVar(key);
  if (!value) return fallback;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? fallback : parsed;
}

// Environment configuration object
export const env: EnvironmentConfig = {
  // Application
  NODE_ENV: (getEnvVar('NODE_ENV', 'development') as EnvironmentConfig['NODE_ENV']),
  APP_NAME: getEnvVar('APP_NAME', 'Attendance Tracking System'),
  APP_SHORT_NAME: getEnvVar('APP_SHORT_NAME', 'ATS'),
  APP_DESCRIPTION: getEnvVar('APP_DESCRIPTION', 'Modern attendance tracking for educational institutions'),
  APP_VERSION: getEnvVar('APP_VERSION', '2.0.0'),
  
  // Supabase (required)
  SUPABASE_URL: getEnvVar('SUPABASE_URL'),
  SUPABASE_ANON_KEY: getEnvVar('SUPABASE_ANON_KEY'),
  
  // Optional Features
  DEV_MODE: getBooleanEnvVar('DEV_MODE', env.NODE_ENV === 'development'),
  SHOW_DEBUG_INFO: getBooleanEnvVar('SHOW_DEBUG_INFO', env.NODE_ENV === 'development'),
  
  // Branding
  CUSTOM_LOGO_URL: getEnvVar('CUSTOM_LOGO_URL'),
  CUSTOM_PRIMARY_COLOR: getEnvVar('CUSTOM_PRIMARY_COLOR'),
  CUSTOM_SECONDARY_COLOR: getEnvVar('CUSTOM_SECONDARY_COLOR'),
  
  // API URLs
  API_BASE_URL: getEnvVar('API_BASE_URL', env.SUPABASE_URL),
  WEBSOCKET_URL: getEnvVar('WEBSOCKET_URL', env.SUPABASE_URL?.replace('https://', 'wss://') + '/realtime/v1'),
  
  // External Services
  SENDGRID_API_KEY: getEnvVar('SENDGRID_API_KEY'),
  TWILIO_ACCOUNT_SID: getEnvVar('TWILIO_ACCOUNT_SID'),
  TWILIO_AUTH_TOKEN: getEnvVar('TWILIO_AUTH_TOKEN'),
  
  // Security
  JWT_SECRET: getEnvVar('JWT_SECRET'),
  ENCRYPTION_KEY: getEnvVar('ENCRYPTION_KEY'),
  
  // Performance
  CACHE_TTL: getNumberEnvVar('CACHE_TTL', 300000), // 5 minutes
  MAX_CONCURRENT_REQUESTS: getNumberEnvVar('MAX_CONCURRENT_REQUESTS', 10),
  REQUEST_TIMEOUT: getNumberEnvVar('REQUEST_TIMEOUT', 30000), // 30 seconds
};

// Validation function to check required environment variables
export function validateEnvironment(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Check required variables
  if (!env.SUPABASE_URL) {
    errors.push('VITE_SUPABASE_URL is required');
  }
  
  if (!env.SUPABASE_ANON_KEY) {
    errors.push('VITE_SUPABASE_ANON_KEY is required');
  }
  
  // Validate URL format
  if (env.SUPABASE_URL && !env.SUPABASE_URL.startsWith('https://')) {
    errors.push('VITE_SUPABASE_URL must be a valid HTTPS URL');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// Development helpers
export const isDevelopment = env.NODE_ENV === 'development';
export const isProduction = env.NODE_ENV === 'production';
export const isTest = env.NODE_ENV === 'test';

// Feature flags
export const features = {
  debugMode: env.DEV_MODE && isDevelopment,
  showDebugInfo: env.SHOW_DEBUG_INFO && isDevelopment,
  enableAnalytics: isProduction,
  enableErrorReporting: isProduction,
  enablePerformanceMonitoring: isProduction,
} as const;
