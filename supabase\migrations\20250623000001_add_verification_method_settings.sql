-- Add verification method settings to school_settings table
-- This migration adds a new column to control verification method requirements

-- Add verification_method_requirement column to school_settings
ALTER TABLE public.school_settings 
ADD COLUMN IF NOT EXISTS verification_method_requirement VARCHAR(20) DEFAULT 'either' 
CHECK (verification_method_requirement IN ('biometric_only', 'pin_only', 'both_required', 'either'));

-- Add comment to explain the options
COMMENT ON COLUMN public.school_settings.verification_method_requirement IS 
'Controls which verification methods students must use:
- biometric_only: Students must use biometric authentication only
- pin_only: Students must use PIN authentication only  
- both_required: Students must use both biometric AND PIN authentication
- either: Students can choose between biometric OR PIN authentication (default)';

-- Update existing school_settings records to have the default value
UPDATE public.school_settings 
SET verification_method_requirement = 'either' 
WHERE verification_method_requirement IS NULL;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_school_settings_verification_method 
ON public.school_settings(verification_method_requirement);

-- Add RLS policy update (if needed)
-- The existing RLS policies should cover this new column automatically
