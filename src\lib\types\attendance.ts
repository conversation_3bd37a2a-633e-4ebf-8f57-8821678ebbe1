/**
 * Attendance and Course Types
 * Type definitions for attendance tracking, courses, and related functionality
 */

export type AttendanceStatus = "present" | "absent" | "late" | "excused";
export type VerificationMethod = "biometric" | "pin" | "manual" | "qr_code";
export type ExcuseStatus = "pending" | "approved" | "rejected";

export interface AttendanceRecord {
  id: string;
  studentId: string;
  roomId: string;
  timestamp: string;
  deviceInfo: string;
  location: {
    latitude: number;
    longitude: number;
  } | any;
  verificationMethod: VerificationMethod;
  status: AttendanceStatus;
  roomName?: string;
  buildingName?: string;
  school_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Course {
  id: string;
  name: string;
  teacherId: string;
  roomId: string;
  school_id?: string;
  schedule: CourseSchedule[];
  created_at?: string;
  updated_at?: string;
}

export interface CourseSchedule {
  day: "monday" | "tuesday" | "wednesday" | "thursday" | "friday" | "saturday" | "sunday";
  startTime: string;
  endTime: string;
}

export interface Excuse {
  id: string;
  student_id: string;
  room_id: string;
  start_date: string;
  end_date: string;
  start_time: string;
  end_time: string;
  reason: string;
  status: ExcuseStatus;
  teacher_id?: string;
  school_id?: string;
  notes?: string;
  created_at: string;
  updated_at: string;

  // Joined fields for display
  studentName?: string;
  roomName?: string;
  teacherName?: string;
}

export interface AttendanceStats {
  totalStudents: number;
  presentCount: number;
  absentCount: number;
  lateCount: number;
  excusedCount: number;
  attendanceRate: number;
  date: string;
  school_id?: string;
}

export interface AttendanceReport {
  id: string;
  school_id: string;
  generated_by: string;
  report_type: "daily" | "weekly" | "monthly" | "custom";
  start_date: string;
  end_date: string;
  filters: {
    blocks?: string[];
    rooms?: string[];
    students?: string[];
  };
  data: AttendanceStats[];
  created_at: string;
}

export interface AttendanceSettings {
  school_id: string;
  start_time: string;
  end_time: string;
  late_threshold_minutes: number;
  grace_period_minutes: number;
  auto_mark_absent_after_minutes: number;
  allow_early_checkin: boolean;
  allow_late_checkout: boolean;
  require_location_verification: boolean;
  require_biometric_verification: boolean;
  updated_at?: string;
}

export interface FraudCase {
  id: string;
  studentId: string;
  attendanceId: string;
  timestamp: string;
  evidenceType: "location_mismatch" | "device_mismatch" | "time_pattern" | "multiple_scans";
  status: "pending" | "reviewing" | "resolved" | "dismissed";
  notes?: string;
  school_id?: string;
  created_at?: string;
  updated_at?: string;
}

export interface AttendanceAlert {
  id: string;
  student_id: string;
  alert_type: "absence" | "late" | "pattern" | "fraud";
  message: string;
  severity: "low" | "medium" | "high";
  acknowledged: boolean;
  school_id?: string;
  created_at: string;
}
