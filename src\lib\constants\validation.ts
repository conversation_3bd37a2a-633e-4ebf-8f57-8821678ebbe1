/**
 * Validation Constants
 * Constants for form validation, data validation, and business rules
 */

export const VALIDATION_RULES = {
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    MAX_LENGTH: 254,
    MIN_LENGTH: 5,
  },
  PASSWORD: {
    MIN_LENGTH: 8,
    MAX_LENGTH: 128,
    PATTERN: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  },
  NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 100,
    PATTERN: /^[a-zA-ZÀ-ÿ\s'-]+$/,
  },
  PHONE: {
    PATTERN: /^\+?[\d\s\-\(\)]+$/,
    MIN_LENGTH: 10,
    MAX_LENGTH: 20,
  },
  STUDENT_ID: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 20,
    PATTERN: /^[A-Za-z0-9\-_]+$/,
  },
  PIN: {
    LENGTH: 4,
    PATTERN: /^\d{4}$/,
  },
  INVITATION_CODE: {
    LENGTH: 8,
    PATTERN: /^[A-Z0-9]{8}$/,
  },
} as const;

export const BUSINESS_RULES = {
  ATTENDANCE: {
    MAX_LATE_MINUTES: 30,
    DEFAULT_GRACE_PERIOD: 5,
    MAX_DAILY_SCANS: 10,
    QR_EXPIRY_MINUTES: 15,
    LOCATION_RADIUS_METERS: 100,
  },
  EXCUSES: {
    MAX_ADVANCE_DAYS: 30,
    MAX_DURATION_DAYS: 7,
    MIN_REASON_LENGTH: 10,
    MAX_REASON_LENGTH: 500,
  },
  USERS: {
    MAX_FAILED_LOGINS: 5,
    LOCKOUT_DURATION_MINUTES: 30,
    SESSION_TIMEOUT_HOURS: 8,
    PASSWORD_RESET_EXPIRY_HOURS: 24,
  },
  SCHOOLS: {
    MAX_STUDENTS_PER_ROOM: 50,
    MAX_ROOMS_PER_BLOCK: 100,
    MAX_BLOCKS_PER_SCHOOL: 20,
    INVITATION_CODE_EXPIRY_DAYS: 30,
  },
} as const;

export const FILE_UPLOAD = {
  MAX_SIZE_MB: 10,
  ALLOWED_IMAGE_TYPES: ["image/jpeg", "image/png", "image/webp", "image/svg+xml"],
  ALLOWED_DOCUMENT_TYPES: ["application/pdf", "text/csv", "application/vnd.ms-excel"],
  MAX_FILES_PER_UPLOAD: 5,
} as const;

export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  MIN_PAGE_SIZE: 5,
} as const;

export const RATE_LIMITS = {
  LOGIN_ATTEMPTS: {
    MAX_ATTEMPTS: 5,
    WINDOW_MINUTES: 15,
  },
  API_REQUESTS: {
    MAX_REQUESTS: 100,
    WINDOW_MINUTES: 1,
  },
  PASSWORD_RESET: {
    MAX_ATTEMPTS: 3,
    WINDOW_HOURS: 1,
  },
} as const;

export const ERROR_MESSAGES = {
  REQUIRED: "This field is required",
  INVALID_EMAIL: "Please enter a valid email address",
  INVALID_PASSWORD: "Password must be at least 8 characters with uppercase, lowercase, number and special character",
  INVALID_PHONE: "Please enter a valid phone number",
  INVALID_NAME: "Name can only contain letters, spaces, hyphens and apostrophes",
  INVALID_STUDENT_ID: "Student ID can only contain letters, numbers, hyphens and underscores",
  INVALID_PIN: "PIN must be exactly 4 digits",
  INVALID_INVITATION_CODE: "Invitation code must be 8 characters (letters and numbers)",
  PASSWORD_MISMATCH: "Passwords do not match",
  FILE_TOO_LARGE: "File size must be less than 10MB",
  INVALID_FILE_TYPE: "Invalid file type",
  NETWORK_ERROR: "Network error. Please check your connection and try again",
  SERVER_ERROR: "Server error. Please try again later",
  UNAUTHORIZED: "You are not authorized to perform this action",
  SESSION_EXPIRED: "Your session has expired. Please log in again",
} as const;

export const SUCCESS_MESSAGES = {
  PROFILE_UPDATED: "Profile updated successfully",
  PASSWORD_CHANGED: "Password changed successfully",
  EMAIL_SENT: "Email sent successfully",
  DATA_SAVED: "Data saved successfully",
  USER_CREATED: "User created successfully",
  USER_UPDATED: "User updated successfully",
  USER_DELETED: "User deleted successfully",
  ATTENDANCE_RECORDED: "Attendance recorded successfully",
  EXCUSE_SUBMITTED: "Excuse submitted successfully",
  SETTINGS_UPDATED: "Settings updated successfully",
} as const;
