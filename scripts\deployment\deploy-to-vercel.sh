#!/bin/bash

# 🚀 Automated Vercel Deployment Script
# This script prepares and deploys the attendance tracking system to Vercel

set -e  # Exit on any error

echo "🚀 Starting Vercel Deployment Process..."
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if git is initialized and clean
print_status "Checking git status..."
if [ ! -d ".git" ]; then
    print_error "Git repository not initialized. Please run 'git init' first."
    exit 1
fi

# Check for uncommitted changes
if [ -n "$(git status --porcelain)" ]; then
    print_warning "You have uncommitted changes. Committing them now..."
    git add .
    git commit -m "feat: prepare for production deployment"
    print_success "Changes committed successfully"
fi

# Install dependencies
print_status "Installing dependencies..."
if command -v bun &> /dev/null; then
    bun install
elif command -v npm &> /dev/null; then
    npm install
else
    print_error "Neither npm nor bun found. Please install Node.js."
    exit 1
fi

# Run build to ensure everything works
print_status "Running production build test..."
if command -v bun &> /dev/null; then
    bun run build
else
    npm run build
fi
print_success "Build completed successfully"

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    print_warning "Vercel CLI not found. Installing..."
    npm install -g vercel
    print_success "Vercel CLI installed"
fi

# Login to Vercel (if not already logged in)
print_status "Checking Vercel authentication..."
if ! vercel whoami &> /dev/null; then
    print_status "Please log in to Vercel..."
    vercel login
fi

# Deploy to Vercel
print_status "Deploying to Vercel..."
vercel --prod

print_success "Deployment completed!"
echo ""
echo "🎉 Your attendance tracking system is now live!"
echo ""
echo "📋 Next Steps:"
echo "1. Configure your custom domain in Vercel dashboard"
echo "2. Set up production environment variables"
echo "3. Test all functionality on the live site"
echo "4. Create user accounts for your pilot schools"
echo ""
echo "📚 Documentation:"
echo "- Deployment Guide: ./DEPLOYMENT_GUIDE.md"
echo "- User Guides: ./docs/user-guides/"
echo ""
echo "🔗 Useful Links:"
echo "- Vercel Dashboard: https://vercel.com/dashboard"
echo "- Supabase Dashboard: https://supabase.com/dashboard"
echo ""
print_success "Deployment script completed successfully!"
