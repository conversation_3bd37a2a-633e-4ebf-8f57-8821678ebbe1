# Documentation

This directory contains all project documentation organized by category:

## 📁 Directory Structure

- **deployment/** - Production deployment guides and checklists
- **development/** - Development setup and contribution guides  
- **features/** - Feature-specific documentation and summaries
- **api/** - API documentation and integration guides
- **user-guides/** - End-user manuals and tutorials

## 🚀 Quick Start

1. For deployment: See `deployment/PRODUCTION_DEPLOYMENT_GUIDE.md`
2. For development: See `development/BRANDING_GUIDE.md`
3. For features: Browse `features/` directory
4. For users: Check `user-guides/README.md`

## 📝 Contributing

When adding new documentation:
1. Place files in the appropriate category directory
2. Update this README if adding new categories
3. Use clear, descriptive filenames
4. Include proper markdown formatting

## 📋 Documentation Categories

### Deployment
- Production deployment guides
- Security and performance optimization
- HTTPS setup and configuration
- Production readiness checklists

### Development
- Branding and theming guides
- Logo and favicon implementation
- Testing strategies
- Tablet setup and improvements

### Features
- Attendance management system
- Automated reminder system
- Enhanced attendance cards
- Notification improvements
- Localization and internationalization

### User Guides
- System permissions and roles
- User manuals and tutorials
- Getting started guides
