import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  Card<PERSON><PERSON><PERSON>,
  CardFooter,
} from "@/components/ui/card";
import {
  Tabs as InnerTabs,
  <PERSON><PERSON><PERSON>ontent as <PERSON>TabsContent,
  <PERSON><PERSON><PERSON><PERSON> as <PERSON>TabsList,
  <PERSON><PERSON><PERSON><PERSON>ger as InnerTabsTrigger,
} from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/lib/supabase";
import { useAuth } from "@/context/AuthContext";
import { Block } from "@/lib/types";
import { AdminBlockLocationSettings } from "@/components/admin/AdminBlockLocationSettings";
import { AdminRoomLocationSettings } from "@/components/admin/AdminRoomLocationSettings";
import AdminLocationVerificationSettings from "@/components/admin/AdminLocationVerificationSettings";
import AdminVerificationMethodSettings from "@/components/admin/AdminVerificationMethodSettings";
import BiometricRegistrationRequests from "@/components/admin/BiometricRegistrationRequests";
import TeacherPermissionsSettings from "@/components/admin/TeacherPermissionsSettings";
import AttendanceSettings from "@/components/admin/AttendanceSettings";
import ParentNotificationSettings from "@/components/admin/ParentNotificationSettings";
import SystemAdminCodeManager from "@/components/admin/SystemAdminCodeManager";
import { isSystemAdmin } from "@/lib/utils/school-context";
import { useTranslation } from "react-i18next";
import { MapPin, Shield, Fingerprint } from "lucide-react";

export default function AdminSettings() {
  const [rooms, setRooms] = useState<
    Array<{
      id: string;
      name: string;
      building: string | null;
      block_id: string;
    }>
  >([]);
  const [blocks, setBlocks] = useState<Block[]>([]);
  const [selectedRoomId, setSelectedRoomId] = useState<string>("");
  const [selectedBlockId, setSelectedBlockId] = useState<string>("");
  const [selectedBlockIdForRooms, setSelectedBlockIdForRooms] = useState<string>("");
  const [filteredRooms, setFilteredRooms] = useState<any[]>([]);
  const [settingsTab, setSettingsTab] = useState<string>("blocks");
  const [verificationSubTab, setVerificationSubTab] = useState<string>("location");
  const [isSystemAdminUser, setIsSystemAdminUser] = useState<boolean>(false);
  const { toast } = useToast();
  const { profile } = useAuth();
  const { t } = useTranslation();

  // Check if the user is a system admin
  useEffect(() => {
    const checkSystemAdminStatus = async () => {
      if (profile?.id) {
        const isAdmin = await isSystemAdmin(profile.id);
        setIsSystemAdminUser(isAdmin);
      }
    };

    checkSystemAdminStatus();
  }, [profile?.id]);

  // Fetch rooms and blocks when component mounts
  useEffect(() => {
    const fetchData = async () => {
      if (!profile?.id || !profile?.school_id) return;

      try {

        // Fetch blocks for the current school only
        const { data: blocksData, error: blocksError } = await supabase
          .from("blocks")
          .select("*")
          .eq("school_id", profile.school_id)
          .order("name");

        if (blocksError) {
          console.error("Error fetching blocks:", blocksError);
          toast({
            title: "Error",
            description: "Failed to load blocks. Please try again.",
            variant: "destructive",
          });
          return;
        }


        if (blocksData) {
          setBlocks(blocksData);
          if (blocksData.length > 0) {
            setSelectedBlockId(blocksData[0].id);
          }
        }

        // Fetch rooms for the current school only
        const { data: roomsData, error: roomsError } = await supabase
          .from("rooms")
          .select("id, name, building, block_id")
          .eq("school_id", profile.school_id)
          .order("name");

        if (roomsError) {
          console.error("Error fetching rooms:", roomsError);
          toast({
            title: "Error",
            description: "Failed to load rooms. Please try again.",
            variant: "destructive",
          });
          return;
        }


        if (roomsData) {
          setRooms(roomsData);
          if (roomsData.length > 0) {
            setSelectedRoomId(roomsData[0].id);
          }
        }
      } catch (error) {
        console.error("Error in fetchData:", error);
        toast({
          title: "Error",
          description: "An unexpected error occurred. Please try again.",
          variant: "destructive",
        });
      }
    };

    fetchData();
  }, [profile?.id, profile?.school_id, toast]);

  // Filter rooms when block selection changes
  useEffect(() => {
    if (selectedBlockIdForRooms && selectedBlockIdForRooms !== "all" && rooms.length > 0) {
      const filtered = rooms.filter(room => room.block_id === selectedBlockIdForRooms);
      setFilteredRooms(filtered);
      // Reset room selection when block changes
      if (filtered.length > 0) {
        setSelectedRoomId(filtered[0].id);
      } else {
        setSelectedRoomId("");
      }
    } else {
      // If no block selected or "all" selected, show all rooms
      setFilteredRooms(rooms);
      if (rooms.length > 0 && !selectedRoomId) {
        setSelectedRoomId(rooms[0].id);
      }
    }
  }, [selectedBlockIdForRooms, rooms, selectedRoomId]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t("admin.settings.systemLocationSettings")}</CardTitle>
        <CardDescription>
          {t("admin.settings.systemLocationDescription")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <InnerTabs value={settingsTab} onValueChange={setSettingsTab}>
          <InnerTabsList className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 xl:grid-cols-6 gap-1 h-auto mb-4">
            <InnerTabsTrigger value="attendance" className="text-xs sm:text-sm px-2 sm:px-3 py-2">
              {t("admin.settings.attendance")}
            </InnerTabsTrigger>
            <InnerTabsTrigger value="blocks" className="text-xs sm:text-sm px-2 sm:px-3 py-2">
              {t("admin.settings.blockLocations")}
            </InnerTabsTrigger>
            <InnerTabsTrigger value="rooms" className="text-xs sm:text-sm px-2 sm:px-3 py-2">
              {t("admin.settings.roomLocations")}
            </InnerTabsTrigger>
            <InnerTabsTrigger value="verification" className="text-xs sm:text-sm px-2 sm:px-3 py-2">
              {t("admin.settings.verification")}
            </InnerTabsTrigger>
            <InnerTabsTrigger value="permissions" className="text-xs sm:text-sm px-2 sm:px-3 py-2">
              {t("admin.settings.permissions")}
            </InnerTabsTrigger>
            <InnerTabsTrigger value="notifications" className="text-xs sm:text-sm px-2 sm:px-3 py-2">
              {t("admin.settings.notifications")}
            </InnerTabsTrigger>
            {isSystemAdminUser && (
              <InnerTabsTrigger value="systemAdmin" className="text-xs sm:text-sm px-2 sm:px-3 py-2 col-span-2 sm:col-span-1">
                {t("admin.settings.systemSettings")}
              </InnerTabsTrigger>
            )}
          </InnerTabsList>

          <InnerTabsContent value="blocks" className="mt-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {t("admin.settings.selectBlock")}
                </label>
                <Select
                  value={selectedBlockId}
                  onValueChange={setSelectedBlockId}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={t("admin.settings.selectBlock")}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {blocks.map((block) => (
                      <SelectItem key={block.id} value={block.id}>
                        Block {block.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedBlockId && blocks.length > 0 && (
                <AdminBlockLocationSettings
                  blockId={selectedBlockId}
                  blockName={
                    blocks.find((b) => b.id === selectedBlockId)?.name || ""
                  }
                />
              )}

              <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-blue-800 text-sm">
                  {t("admin.settings.blockLocationInfo")}
                </p>
              </div>
            </div>
          </InnerTabsContent>

          <InnerTabsContent value="rooms" className="mt-6">
            <div className="space-y-4">
              {/* Block selector for filtering rooms */}
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {t("admin.settings.selectBlock")}
                </label>
                <Select
                  value={selectedBlockIdForRooms}
                  onValueChange={setSelectedBlockIdForRooms}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t("admin.settings.selectBlockFirst")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      {t("admin.settings.allBlocks")}
                    </SelectItem>
                    {blocks.map((block) => (
                      <SelectItem key={block.id} value={block.id}>
                        Block {block.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Room selector - filtered by selected block */}
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {t("admin.settings.selectRoom")}
                </label>
                <Select
                  value={selectedRoomId}
                  onValueChange={setSelectedRoomId}
                  disabled={selectedBlockIdForRooms && selectedBlockIdForRooms !== "all" && filteredRooms.length === 0}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={
                        selectedBlockIdForRooms && selectedBlockIdForRooms !== "all" && filteredRooms.length === 0
                          ? t("admin.settings.noRoomsInBlock")
                          : t("admin.settings.selectRoom")
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredRooms.map((room) => (
                      <SelectItem key={room.id} value={room.id}>
                        Room {room.name}
                        {room.building ? ` (${room.building})` : ""}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {selectedBlockIdForRooms && selectedBlockIdForRooms !== "all" && (
                  <p className="text-sm text-muted-foreground">
                    {t("admin.settings.showingRoomsFromBlock", {
                      blockName: blocks.find(b => b.id === selectedBlockIdForRooms)?.name || "",
                      count: filteredRooms.length
                    })}
                  </p>
                )}
                {selectedBlockIdForRooms === "all" && (
                  <p className="text-sm text-muted-foreground">
                    {t("admin.settings.showingAllRooms", {
                      count: filteredRooms.length
                    })}
                  </p>
                )}
              </div>

              {selectedRoomId && (
                <AdminRoomLocationSettings roomId={selectedRoomId} />
              )}

              <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <p className="text-amber-800 text-sm">
                  {t("admin.settings.roomLocationInfo")}
                </p>
              </div>
            </div>
          </InnerTabsContent>

          <InnerTabsContent value="verification" className="mt-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {t("admin.settings.configureLocationVerification")}
                </label>
                <p className="text-sm text-muted-foreground">
                  {t("admin.settings.controlLocationVerification")}
                </p>
              </div>

              {/* Nested tabs for verification settings */}
              <InnerTabs
                value={verificationSubTab}
                onValueChange={setVerificationSubTab}
              >
                <InnerTabsList className="grid w-full max-w-3xl mx-auto grid-cols-3">
                  <InnerTabsTrigger
                    value="location"
                    className="flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-2 py-2"
                  >
                    <MapPin className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="text-[10px] sm:text-sm">
                      {t("admin.settings.locationVerification")}
                    </span>
                  </InnerTabsTrigger>
                  <InnerTabsTrigger
                    value="methods"
                    className="flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-2 py-2"
                  >
                    <Shield className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="text-[10px] sm:text-sm">
                      {t("admin.settings.verificationMethods")}
                    </span>
                  </InnerTabsTrigger>
                  <InnerTabsTrigger
                    value="biometric-requests"
                    className="flex flex-col sm:flex-row items-center justify-center gap-1 sm:gap-2 py-2"
                  >
                    <Fingerprint className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span className="text-[10px] sm:text-sm">
                      {t("admin.settings.biometricRequests")}
                    </span>
                  </InnerTabsTrigger>
                </InnerTabsList>

                <InnerTabsContent value="location" className="mt-6">
                  <div className="space-y-4">
                    {selectedRoomId && (
                      <AdminLocationVerificationSettings
                        roomId={selectedRoomId}
                        blockId={
                          rooms.find((r) => r.id === selectedRoomId)?.block_id || ""
                        }
                      />
                    )}

                    <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
                      <p className="text-green-800 text-sm">
                        {t("admin.settings.verificationInfo")}
                      </p>
                    </div>
                  </div>
                </InnerTabsContent>

                <InnerTabsContent value="methods" className="mt-6">
                  <AdminVerificationMethodSettings
                    roomId={selectedRoomId}
                    blockId={
                      rooms.find((r) => r.id === selectedRoomId)?.block_id || ""
                    }
                  />
                </InnerTabsContent>

                <InnerTabsContent value="biometric-requests" className="mt-6">
                  <BiometricRegistrationRequests />
                </InnerTabsContent>
              </InnerTabs>
            </div>
          </InnerTabsContent>

          <InnerTabsContent value="permissions" className="mt-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {t("admin.settings.teacherPermissions")}
                </label>
                <p className="text-sm text-muted-foreground">
                  {t("admin.settings.controlTeacherPermissions")}
                </p>
              </div>

              <TeacherPermissionsSettings />

              <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-blue-800 text-sm">
                  {t("admin.settings.teacherPermissionsInfo")}
                </p>
              </div>
            </div>
          </InnerTabsContent>



          <InnerTabsContent value="attendance" className="mt-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {t("admin.settings.attendanceRecordingSettings")}
                </label>
                <p className="text-sm text-muted-foreground">
                  {t("admin.settings.controlAttendanceRecording")}
                </p>
              </div>

              <AttendanceSettings />

              <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-blue-800 text-sm">
                  {t("admin.settings.attendanceTimeRestrictionInfo")}
                </p>
              </div>
            </div>
          </InnerTabsContent>

          <InnerTabsContent value="notifications" className="mt-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {t("admin.settings.parentNotificationSettings")}
                </label>
                <p className="text-sm text-muted-foreground">
                  {t("admin.settings.configureParentNotifications")}
                </p>
              </div>

              <ParentNotificationSettings />

              <div className="mt-4 p-4 bg-purple-50 border border-purple-200 rounded-md">
                <p className="text-purple-800 text-sm">
                  {t("admin.settings.parentNotificationInfo")}
                </p>
              </div>
            </div>
          </InnerTabsContent>



          {isSystemAdminUser && (
            <InnerTabsContent value="systemAdmin" className="mt-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    {t("admin.settings.systemAdminSettings")}
                  </label>
                  <p className="text-sm text-muted-foreground">
                    {t("admin.settings.manageSystemSettings")}
                  </p>
                </div>

                <SystemAdminCodeManager />

                <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-800 text-sm">
                    {t("admin.settings.systemAdminOnlyInfo")}
                  </p>
                </div>
              </div>
            </InnerTabsContent>
          )}
        </InnerTabs>
      </CardContent>
      <CardFooter className="text-sm text-muted-foreground">
        {t("admin.settings.attendanceFooter")}
      </CardFooter>
    </Card>
  );
}
