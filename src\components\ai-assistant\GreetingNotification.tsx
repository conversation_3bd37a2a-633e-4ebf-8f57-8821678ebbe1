import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON>rk<PERSON>, X } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useState, useEffect } from 'react';

interface GreetingNotificationProps {
  isVisible: boolean;
  onClose: () => void;
  greetingType: 'welcomeBack' | 'firstTime' | 'newUser';
}

export default function GreetingNotification({ 
  isVisible, 
  onClose, 
  greetingType 
}: GreetingNotificationProps) {
  const { t } = useTranslation();
  const [displayedText, setDisplayedText] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  const greetingConfig = {
    welcomeBack: {
      title: t('aiAssistant.greetings.welcomeBack'),
      description: t('aiAssistant.greetings.welcomeBackDescription'),
      gradient: 'from-blue-500/20 to-purple-500/20',
      borderGradient: 'from-blue-500/40 to-purple-500/40'
    },
    firstTime: {
      title: t('aiAssistant.greetings.firstTimeWelcome'),
      description: t('aiAssistant.greetings.firstTimeDescription'),
      gradient: 'from-green-500/20 to-emerald-500/20',
      borderGradient: 'from-green-500/40 to-emerald-500/40'
    },
    newUser: {
      title: t('aiAssistant.greetings.newUserWelcome'),
      description: t('aiAssistant.greetings.newUserDescription'),
      gradient: 'from-[#EE0D09]/20 to-[#FF1A1A]/20',
      borderGradient: 'from-[#EE0D09]/40 to-[#FF1A1A]/40'
    }
  };

  const config = greetingConfig[greetingType];

  // Typewriter effect for description
  useEffect(() => {
    if (!isVisible) {
      setDisplayedText('');
      setIsTyping(false);
      return;
    }

    const fullText = config.description;
    setDisplayedText('');
    setIsTyping(true);

    // Start typing after title animation (delay of 0.8s)
    const startDelay = setTimeout(() => {
      let currentIndex = 0;

      const typeInterval = setInterval(() => {
        if (currentIndex <= fullText.length) {
          setDisplayedText(fullText.slice(0, currentIndex));
          currentIndex++;
        } else {
          setIsTyping(false);
          clearInterval(typeInterval);
        }
      }, 50); // 50ms per character for smooth typing

      return () => clearInterval(typeInterval);
    }, 800);

    return () => {
      clearTimeout(startDelay);
      setIsTyping(false);
    };
  }, [isVisible, config.description]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 20, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -20, scale: 0.9 }}
          transition={{ 
            duration: 0.5, 
            ease: "easeOut",
            exit: { duration: 0.3 }
          }}
          className="fixed bottom-20 right-4 md:bottom-24 md:right-6 z-40 max-w-[280px] md:max-w-[320px]"
        >
          {/* Main notification card */}
          <div className="relative">
            {/* Background with gradient */}
            <div className={`absolute inset-0 bg-gradient-to-br ${config.gradient} backdrop-blur-sm rounded-2xl`} />
            
            {/* Border gradient */}
            <div className={`absolute inset-0 bg-gradient-to-br ${config.borderGradient} rounded-2xl p-[1px]`}>
              <div className="bg-gray-900/95 backdrop-blur-sm rounded-2xl h-full w-full" />
            </div>

            {/* Content */}
            <div className="relative p-4 md:p-5">
              {/* Header */}
              <div className="flex items-start gap-3 mb-3">
                {/* AI Avatar */}
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.2, duration: 0.4, type: "spring" }}
                  className="w-8 h-8 md:w-9 md:h-9 rounded-full bg-gradient-to-br from-[#EE0D09] to-[#FF1A1A] flex items-center justify-center flex-shrink-0"
                >
                  <Bot className="w-4 h-4 md:w-5 md:h-5 text-white" />
                </motion.div>

                {/* Title and close button */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <motion.h3
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3, duration: 0.3 }}
                      className="text-white font-semibold text-sm md:text-base flex items-center gap-2"
                    >
                      {config.title}
                      <motion.div
                        animate={{ rotate: [0, 15, -15, 0] }}
                        transition={{ delay: 0.5, duration: 2, repeat: Infinity, repeatDelay: 3 }}
                      >
                        <Sparkles className="w-3 h-3 md:w-4 md:h-4 text-[#EE0D09]" />
                      </motion.div>
                    </motion.h3>

                    <Button
                      onClick={onClose}
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0 text-gray-400 hover:text-white hover:bg-white/10 rounded-full"
                    >
                      <X className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Description with typewriter effect */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.3 }}
                className="text-gray-300 text-xs md:text-sm leading-relaxed ml-11 md:ml-12"
              >
                <span>{displayedText}</span>
                {isTyping && (
                  <motion.span
                    animate={{ opacity: [1, 0] }}
                    transition={{ duration: 0.8, repeat: Infinity }}
                    className="text-[#EE0D09]"
                  >
                    |
                  </motion.span>
                )}
              </motion.div>

              {/* Animated dots indicator */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6, duration: 0.3 }}
                className="flex items-center gap-1 mt-3 ml-11 md:ml-12"
              >
                {[0, 1, 2].map((index) => (
                  <motion.div
                    key={index}
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 1, 0.5]
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      delay: index * 0.2
                    }}
                    className="w-1 h-1 bg-[#EE0D09] rounded-full"
                  />
                ))}
              </motion.div>
            </div>

            {/* Subtle glow effect */}
            <motion.div
              animate={{
                opacity: [0.3, 0.6, 0.3],
                scale: [1, 1.02, 1]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className={`absolute -inset-1 bg-gradient-to-br ${config.borderGradient} rounded-2xl blur-sm -z-10`}
            />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
