import { motion } from 'framer-motion';
import { <PERSON><PERSON>, User, Spark<PERSON>, Volume2, VolumeX } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ChatMessage } from '@/lib/services/ai-assistant-service';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useTranslation } from 'react-i18next';

interface MessageBubbleProps {
  message: ChatMessage;
  onActionClick?: (message: string) => void;
  onSpeakMessage?: (text: string) => void;
  onStopSpeaking?: () => void;
  isSpeaking?: boolean;
}

export default function MessageBubble({
  message,
  onActionClick,
  onSpeakMessage,
  onStopSpeaking,
  isSpeaking
}: MessageBubbleProps) {
  const { t } = useTranslation();
  const isBot = message.type === 'bot';
  const isUser = message.type === 'user';

  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className={`flex gap-2 md:gap-3 ${isUser ? 'flex-row-reverse' : 'flex-row'}`}
    >
      {/* Avatar */}
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.1, type: "spring", stiffness: 300 }}
        className={`w-7 h-7 md:w-8 md:h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
          isBot
            ? 'bg-gradient-to-br from-[#EE0D09] to-[#FF1A1A]'
            : 'bg-gradient-to-br from-blue-500 to-blue-600'
        }`}
      >
        {isBot ? (
          <Bot className="w-3 h-3 md:w-4 md:h-4 text-white" />
        ) : (
          <User className="w-3 h-3 md:w-4 md:h-4 text-white" />
        )}
      </motion.div>

      {/* Message content */}
      <div className={`flex-1 max-w-[240px] md:max-w-[280px] ${isUser ? 'items-end' : 'items-start'} flex flex-col`}>
        {/* Message bubble */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{
            scale: 1,
            opacity: 1,
            // Subtle pulse when typing
            ...(isBot && message.isTyping && {
              boxShadow: [
                '0 0 0 0 rgba(238, 13, 9, 0.3)',
                '0 0 0 4px rgba(238, 13, 9, 0.1)',
                '0 0 0 0 rgba(238, 13, 9, 0.3)'
              ]
            })
          }}
          transition={{
            delay: 0.2,
            duration: 0.3,
            ...(isBot && message.isTyping && {
              boxShadow: {
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }
            })
          }}
          className={`relative px-3 md:px-4 py-2 md:py-3 rounded-2xl ${
            isBot
              ? 'bg-white/10 backdrop-blur-sm border border-white/20 text-white'
              : 'bg-gradient-to-r from-blue-500 to-blue-600 text-white ml-auto'
          } ${
            isBot ? 'rounded-bl-md' : 'rounded-br-md'
          }`}
        >
          {/* Bot message styling with gradient border */}
          {isBot && (
            <div className="absolute inset-0 rounded-2xl rounded-bl-md bg-gradient-to-r from-[#EE0D09]/20 to-[#FF1A1A]/20 -z-10" />
          )}

          {/* Message text */}
          <div className="text-xs md:text-sm leading-relaxed">
            {isBot ? (
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={{
                  strong: ({ children }) => <strong className="font-semibold">{children}</strong>,
                  h1: ({ children }) => <div className="font-semibold text-base mt-2 mb-1">{children}</div>,
                  h2: ({ children }) => <div className="font-semibold text-sm mt-2 mb-1">{children}</div>,
                  h3: ({ children }) => <div className="font-medium text-sm mt-1 mb-1">{children}</div>,
                  code: ({ children }) => <code className="bg-white/10 px-1 py-0.5 rounded text-xs">{children}</code>,
                  p: ({ children }) => <div className="mb-1 last:mb-0">{children}</div>,
                  ul: ({ children }) => <ul className="list-disc list-inside ml-2 space-y-1">{children}</ul>,
                  ol: ({ children }) => <ol className="list-decimal list-inside ml-2 space-y-1">{children}</ol>,
                  li: ({ children }) => <li className="text-xs md:text-sm">{children}</li>
                }}
              >
                {message.content}
              </ReactMarkdown>
            ) : (
              message.content
            )}
            {/* Typing cursor for bot messages */}
            {isBot && message.isTyping && (
              <motion.span
                animate={{ opacity: [1, 0, 1] }}
                transition={{ duration: 0.8, repeat: Infinity, ease: "easeInOut" }}
                className="inline-block w-0.5 h-3 md:h-4 bg-white ml-1 rounded-sm"
              />
            )}
          </div>

          {/* Text-to-Speech button for bot messages */}
          {isBot && onSpeakMessage && !message.isTyping && (
            <div className="flex justify-end mt-2">
              <Button
                onClick={() => {
                  if (isSpeaking) {
                    onStopSpeaking?.();
                  } else {
                    onSpeakMessage(message.content);
                  }
                }}
                size="sm"
                variant="ghost"
                className="h-6 w-6 p-0 text-gray-400 hover:text-white hover:bg-white/10 rounded-full"
                title={isSpeaking ? t('aiAssistant.stopSpeaking') : t('aiAssistant.speakMessage')}
              >
                {isSpeaking ? (
                  <VolumeX className="w-3 h-3" />
                ) : (
                  <Volume2 className="w-3 h-3" />
                )}
              </Button>
            </div>
          )}

          {/* Bot sparkle indicator */}
          {isBot && (
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ delay: 0.5, duration: 0.3 }}
              className="absolute -top-1 -right-1"
            >
              <Sparkles className="w-2 h-2 md:w-3 md:h-3 text-[#EE0D09]" />
            </motion.div>
          )}
        </motion.div>

        {/* Action buttons for bot messages */}
        {isBot && message.actions && message.actions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.3 }}
            className="mt-2 md:mt-3 flex flex-wrap gap-1 md:gap-2"
          >
            {message.actions.map((action, index) => (
              <motion.div
                key={action.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 + index * 0.1, duration: 0.3 }}
              >
                <Button
                  onClick={() => {
                    if (action.message && onActionClick) {
                      onActionClick(action.message);
                    } else if (action.action) {
                      action.action();
                    }
                  }}
                  size="sm"
                  variant="outline"
                  className="h-7 md:h-8 px-2 md:px-3 text-xs bg-white/5 border-white/20 text-white hover:bg-white/10 hover:border-[#EE0D09]/50 transition-all duration-200"
                >
                  <motion.span
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {action.label}
                  </motion.span>
                </Button>
              </motion.div>
            ))}
          </motion.div>
        )}

        {/* Timestamp */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.3 }}
          className={`mt-1 text-xs text-gray-400 ${isUser ? 'text-right' : 'text-left'}`}
        >
          {message.timestamp.toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </motion.div>
      </div>
    </motion.div>
  );
}
