import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";
import { useAuth } from "@/context/AuthContext";
import { supabase } from "@/lib/supabase";
import { 
  Fingerprint, 
  KeyRound, 
  Shield, 
  Users, 
  CheckCircle2, 
  AlertCircle,
  Info,
  Loader2,
  Save,
  Settings
} from "lucide-react";

interface AdminVerificationMethodSettingsProps {
  roomId?: string;
  blockId?: string;
}

type VerificationMethod = 'biometric_only' | 'pin_only' | 'both_required' | 'either';

interface VerificationSettings {
  verification_method_requirement: VerificationMethod;
  require_biometric_verification: boolean;
  allow_pin_verification: boolean;
}

export default function AdminVerificationMethodSettings({ 
  roomId, 
  blockId 
}: AdminVerificationMethodSettingsProps) {
  const [settings, setSettings] = useState<VerificationSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();
  const { t } = useTranslation();
  const { profile } = useAuth();

  // Fetch current settings
  useEffect(() => {
    const fetchSettings = async () => {
      if (!profile?.school_id) return;

      setLoading(true);
      try {
        const { data, error } = await supabase
          .from("school_settings")
          .select("verification_method_requirement, require_biometric_verification, allow_pin_verification")
          .eq("school_id", profile.school_id)
          .single();

        if (error && error.code !== "PGRST116") {
          console.error("Error fetching verification settings:", error);
          // Set default settings
          setSettings({
            verification_method_requirement: 'either',
            require_biometric_verification: false,
            allow_pin_verification: true,
          });
          return;
        }

        if (data) {
          setSettings({
            verification_method_requirement: data.verification_method_requirement || 'either',
            require_biometric_verification: data.require_biometric_verification || false,
            allow_pin_verification: data.allow_pin_verification !== false, // Default to true
          });
        } else {
          // No settings found, create default
          setSettings({
            verification_method_requirement: 'either',
            require_biometric_verification: false,
            allow_pin_verification: true,
          });
        }
      } catch (error) {
        console.error("Error fetching verification settings:", error);
        toast({
          title: t("admin.settings.errorLoadingSettings"),
          description: t("admin.settings.errorLoadingSettingsMessage"),
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [profile?.school_id, toast, t]);

  // Save settings
  const saveSettings = async () => {
    if (!settings || !profile?.school_id) return;

    setSaving(true);
    try {
      // Update or insert school settings
      const { error } = await supabase
        .from("school_settings")
        .upsert({
          school_id: profile.school_id,
          verification_method_requirement: settings.verification_method_requirement,
          require_biometric_verification: settings.require_biometric_verification,
          allow_pin_verification: settings.allow_pin_verification,
          updated_at: new Date().toISOString(),
        }, {
          onConflict: 'school_id'
        });

      if (error) {
        throw error;
      }

      toast({
        title: t("admin.settings.settingsSaved"),
        description: t("admin.settings.verificationMethodSettingsSaved"),
      });
    } catch (error) {
      console.error("Error saving verification settings:", error);
      toast({
        title: t("admin.settings.errorSavingSettings"),
        description: t("admin.settings.errorSavingSettingsMessage"),
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle verification method change
  const handleVerificationMethodChange = (value: VerificationMethod) => {
    setSettings(prev => prev ? {
      ...prev,
      verification_method_requirement: value,
      // Auto-update related settings based on selection
      require_biometric_verification: value === 'biometric_only' || value === 'both_required',
      allow_pin_verification: value === 'pin_only' || value === 'both_required' || value === 'either',
    } : null);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            {t("admin.settings.verificationMethodSettings")}
          </CardTitle>
          <CardDescription>
            {t("admin.settings.verificationMethodDescription")}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">{t("common.loading")}</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          {t("admin.settings.verificationMethodSettings")}
        </CardTitle>
        <CardDescription>
          {t("admin.settings.verificationMethodDescription")}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* School-wide Settings Notice */}
        <Alert>
          <Settings className="h-4 w-4" />
          <AlertDescription>
            {t("admin.settings.schoolWideVerificationInfo")}
          </AlertDescription>
        </Alert>

        {/* Verification Method Selection */}
        <div className="space-y-4">
          <Label className="text-base font-medium">
            {t("admin.settings.selectVerificationMethod")}
          </Label>
          
          <RadioGroup
            value={settings?.verification_method_requirement || 'either'}
            onValueChange={handleVerificationMethodChange}
            className="space-y-4"
          >
            {/* Either Option (Default) */}
            <div className="flex items-start space-x-3 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
              <RadioGroupItem value="either" id="either" className="mt-1" />
              <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="either" className="font-medium cursor-pointer">
                    {t("admin.settings.verificationEither")}
                  </Label>
                  <Badge variant="secondary">{t("admin.settings.recommended")}</Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {t("admin.settings.verificationEitherDescription")}
                </p>
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <Fingerprint className="h-4 w-4 text-blue-500" />
                    <span>{t("admin.settings.biometricOptional")}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <KeyRound className="h-4 w-4 text-green-500" />
                    <span>{t("admin.settings.pinOptional")}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Biometric Only */}
            <div className="flex items-start space-x-3 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
              <RadioGroupItem value="biometric_only" id="biometric_only" className="mt-1" />
              <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="biometric_only" className="font-medium cursor-pointer">
                    {t("admin.settings.verificationBiometricOnly")}
                  </Label>
                  <Badge variant="outline" className="border-blue-500 text-blue-700">
                    {t("admin.settings.highSecurity")}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {t("admin.settings.verificationBiometricOnlyDescription")}
                </p>
                <div className="flex items-center gap-1 text-sm">
                  <Fingerprint className="h-4 w-4 text-blue-500" />
                  <span className="text-blue-700 font-medium">{t("admin.settings.biometricRequired")}</span>
                </div>
              </div>
            </div>

            {/* PIN Only */}
            <div className="flex items-start space-x-3 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
              <RadioGroupItem value="pin_only" id="pin_only" className="mt-1" />
              <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="pin_only" className="font-medium cursor-pointer">
                    {t("admin.settings.verificationPinOnly")}
                  </Label>
                  <Badge variant="outline" className="border-green-500 text-green-700">
                    {t("admin.settings.compatible")}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {t("admin.settings.verificationPinOnlyDescription")}
                </p>
                <div className="flex items-center gap-1 text-sm">
                  <KeyRound className="h-4 w-4 text-green-500" />
                  <span className="text-green-700 font-medium">{t("admin.settings.pinRequired")}</span>
                </div>
              </div>
            </div>

            {/* Both Required */}
            <div className="flex items-start space-x-3 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
              <RadioGroupItem value="both_required" id="both_required" className="mt-1" />
              <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="both_required" className="font-medium cursor-pointer">
                    {t("admin.settings.verificationBothRequired")}
                  </Label>
                  <Badge variant="outline" className="border-red-500 text-red-700">
                    {t("admin.settings.maximumSecurity")}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {t("admin.settings.verificationBothRequiredDescription")}
                </p>
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <Fingerprint className="h-4 w-4 text-blue-500" />
                    <span className="text-blue-700 font-medium">{t("admin.settings.biometricRequired")}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <KeyRound className="h-4 w-4 text-green-500" />
                    <span className="text-green-700 font-medium">{t("admin.settings.pinRequired")}</span>
                  </div>
                </div>
              </div>
            </div>
          </RadioGroup>
        </div>

        {/* Save Button */}
        <div className="flex justify-end pt-4">
          <Button 
            onClick={saveSettings} 
            disabled={saving}
            className="min-w-[120px]"
          >
            {saving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {t("common.saving")}
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {t("admin.settings.saveSettings")}
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
