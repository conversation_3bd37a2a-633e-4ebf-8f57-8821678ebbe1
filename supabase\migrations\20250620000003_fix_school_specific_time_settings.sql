-- CRITICAL FIX: Make attendance and excuse time settings school-specific
-- This ensures each school can have their own time ranges

-- Add school_id to attendance_settings table
ALTER TABLE attendance_settings 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- Add school_id to excuse_settings table  
ALTER TABLE excuse_settings 
ADD COLUMN IF NOT EXISTS school_id UUID REFERENCES schools(id) ON DELETE CASCADE;

-- Create unique constraint to ensure one setting per school
ALTER TABLE attendance_settings 
DROP CONSTRAINT IF EXISTS attendance_settings_school_unique;
ALTER TABLE attendance_settings 
ADD CONSTRAINT attendance_settings_school_unique UNIQUE (school_id);

ALTER TABLE excuse_settings 
DROP CONSTRAINT IF EXISTS excuse_settings_school_unique;
ALTER TABLE excuse_settings 
ADD CONSTRAINT excuse_settings_school_unique UNIQUE (school_id);

-- Migrate existing settings to each school with default values
DO $$
DECLARE
    school_record RECORD;
    existing_attendance RECORD;
    existing_excuse RECORD;
BEGIN
    -- Get existing global settings if any
    SELECT * INTO existing_attendance FROM attendance_settings LIMIT 1;
    SELECT * INTO existing_excuse FROM excuse_settings LIMIT 1;
    
    -- For each school, create default time settings
    FOR school_record IN SELECT id FROM schools LOOP
        -- Create attendance settings for this school
        INSERT INTO attendance_settings (
            school_id,
            recording_start_time,
            recording_end_time,
            created_at,
            updated_at
        ) VALUES (
            school_record.id,
            COALESCE(existing_attendance.recording_start_time, '08:00:00'), -- Default 8 AM
            COALESCE(existing_attendance.recording_end_time, '17:00:00'),   -- Default 5 PM
            NOW(),
            NOW()
        ) ON CONFLICT (school_id) DO NOTHING;
        
        -- Create excuse settings for this school
        INSERT INTO excuse_settings (
            school_id,
            submission_start_time,
            submission_end_time,
            max_days_in_advance,
            max_excuse_duration_days,
            created_at,
            updated_at
        ) VALUES (
            school_record.id,
            COALESCE(existing_excuse.submission_start_time, '08:00:00'),    -- Default 8 AM
            COALESCE(existing_excuse.submission_end_time, '17:00:00'),      -- Default 5 PM
            COALESCE(existing_excuse.max_days_in_advance, 7),               -- Default 7 days
            COALESCE(existing_excuse.max_excuse_duration_days, 30),         -- Default 30 days
            NOW(),
            NOW()
        ) ON CONFLICT (school_id) DO NOTHING;
    END LOOP;
    
    -- Remove old global settings that don't have school_id
    DELETE FROM attendance_settings WHERE school_id IS NULL;
    DELETE FROM excuse_settings WHERE school_id IS NULL;
    
    RAISE NOTICE 'Created school-specific time settings for % schools', (SELECT COUNT(*) FROM schools);
END $$;

-- Make school_id NOT NULL after migration
ALTER TABLE attendance_settings ALTER COLUMN school_id SET NOT NULL;
ALTER TABLE excuse_settings ALTER COLUMN school_id SET NOT NULL;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_attendance_settings_school_id ON attendance_settings(school_id);
CREATE INDEX IF NOT EXISTS idx_excuse_settings_school_id ON excuse_settings(school_id);

-- Update RLS policies for attendance_settings
DROP POLICY IF EXISTS "School admins can manage attendance settings" ON attendance_settings;
CREATE POLICY "School admins can manage attendance settings"
ON attendance_settings
FOR ALL
TO authenticated
USING (
  -- Users can access settings for their own school
  school_id = (SELECT school_id FROM profiles WHERE user_id = auth.uid())
  OR
  -- System admins can access all settings
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE user_id = auth.uid() 
    AND role = 'admin' 
    AND access_level = 3
  )
)
WITH CHECK (
  -- Users can modify settings for their own school
  school_id = (SELECT school_id FROM profiles WHERE user_id = auth.uid())
  OR
  -- System admins can modify all settings
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE user_id = auth.uid() 
    AND role = 'admin' 
    AND access_level = 3
  )
);

-- Update RLS policies for excuse_settings
DROP POLICY IF EXISTS "School admins can manage excuse settings" ON excuse_settings;
CREATE POLICY "School admins can manage excuse settings"
ON excuse_settings
FOR ALL
TO authenticated
USING (
  -- Users can access settings for their own school
  school_id = (SELECT school_id FROM profiles WHERE user_id = auth.uid())
  OR
  -- System admins can access all settings
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE user_id = auth.uid() 
    AND role = 'admin' 
    AND access_level = 3
  )
)
WITH CHECK (
  -- Users can modify settings for their own school
  school_id = (SELECT school_id FROM profiles WHERE user_id = auth.uid())
  OR
  -- System admins can modify all settings
  EXISTS (
    SELECT 1 FROM profiles 
    WHERE user_id = auth.uid() 
    AND role = 'admin' 
    AND access_level = 3
  )
);

-- Enable RLS on both tables
ALTER TABLE attendance_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE excuse_settings ENABLE ROW LEVEL SECURITY;

-- Add comments
COMMENT ON COLUMN attendance_settings.school_id IS 'School-specific attendance time settings';
COMMENT ON COLUMN excuse_settings.school_id IS 'School-specific excuse submission time settings';
