-- CRITICAL FIX: Move social media integration from global system_settings to school-specific storage
-- This ensures social media configurations are isolated by school

-- Add social_media_integration column to school_settings table
ALTER TABLE school_settings 
ADD COLUMN IF NOT EXISTS social_media_integration JSONB DEFAULT '{"enabled": false, "platforms": {}, "refresh_interval_minutes": 30, "show_engagement_stats": true}';

-- Migrate existing social media settings from system_settings to school_settings
-- This will copy the global setting to all schools as a starting point
DO $$
DECLARE
  existing_settings JSONB;
  school_record RECORD;
BEGIN
  -- Get existing social media settings from system_settings
  SELECT setting_value INTO existing_settings 
  FROM system_settings 
  WHERE setting_name = 'social_media_integration';
  
  -- If settings exist, copy them to all schools
  IF existing_settings IS NOT NULL THEN
    FOR school_record IN SELECT id FROM schools LOOP
      -- Update or insert school settings with the social media configuration
      INSERT INTO school_settings (school_id, social_media_integration, created_at, updated_at)
      VALUES (school_record.id, existing_settings, NOW(), NOW())
      ON CONFLICT (school_id) 
      DO UPDATE SET 
        social_media_integration = existing_settings,
        updated_at = NOW();
    END LOOP;
  END IF;
END $$;

-- Remove the global social media integration setting from system_settings
-- since it should now be school-specific
DELETE FROM system_settings WHERE setting_name = 'social_media_integration';

-- Add comment to document the change
COMMENT ON COLUMN school_settings.social_media_integration IS 'School-specific social media integration settings including platform configurations and display preferences';
