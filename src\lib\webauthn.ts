import { supabase } from "./supabase";

// Convert ArrayBuffer to Base64URL string
const bufferToBase64URL = (buffer: ArrayBuffer): string => {
  // Convert ArrayBuffer to Base64
  const base64 = btoa(String.fromCharCode(...new Uint8Array(buffer)));
  // Convert Base64 to Base64URL
  return base64.replace(/\+/g, "-").replace(/\//g, "_").replace(/=/g, "");
};

// Convert Base64URL string to ArrayBuffer
const base64URLToBuffer = (base64url: string): ArrayBuffer => {
  // Convert Base64URL to Base64
  const base64 = base64url.replace(/-/g, "+").replace(/_/g, "/");
  // Add padding if needed
  const padded = base64.padEnd(
    base64.length + ((4 - (base64.length % 4)) % 4),
    "="
  );
  // Convert Base64 to binary string
  const binary = atob(padded);
  // Convert binary string to <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  const buffer = new ArrayBuffer(binary.length);
  const bytes = new Uint8Array(buffer);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return buffer;
};

// Get the domain without port number
const getDomain = () => {
  const hostname = window.location.hostname;

  // For localhost development, we need to handle WebAuthn differently
  if (
    hostname === "localhost" ||
    hostname === "127.0.0.1" ||
    hostname.includes("localhost")
  ) {
    // For development, we'll use localhost but need to ensure HTTPS or special handling
    return "localhost";
  }

  // For IP addresses (like 192.168.x.x), WebAuthn requires special handling
  // IP addresses are not valid RP IDs, so we need to use localhost for development
  const isIPAddress = /^(\d{1,3}\.){3}\d{1,3}$/.test(hostname);
  if (isIPAddress) {
    console.warn(
      `WebAuthn: IP address detected (${hostname}), using localhost as RP ID`
    );
    return "localhost";
  }

  // For production, use the actual hostname
  return hostname;
};

// Check if we're in a secure context (required for WebAuthn)
const isSecureContext = () => {
  const hostname = window.location.hostname;
  const isIPAddress = /^(\d{1,3}\.){3}\d{1,3}$/.test(hostname);

  // WebAuthn requires HTTPS except for localhost and local IP addresses in development
  return (
    window.isSecureContext ||
    hostname === "localhost" ||
    hostname === "127.0.0.1" ||
    window.location.protocol === "https:" ||
    (isIPAddress && window.location.protocol === "https:") // Allow HTTPS on IP addresses
  );
};

// Check if WebAuthn is available
export const isWebAuthnAvailable = () => {
  return (
    window.PublicKeyCredential !== undefined &&
    typeof window.PublicKeyCredential === "function" &&
    isSecureContext()
  );
};

/**
 * SECURITY ENHANCEMENT: Strict Biometric-Only Authentication
 *
 * This implementation enforces biometric-only authentication by:
 * 1. Setting authenticatorAttachment: "platform" - Only built-in authenticators
 * 2. Setting userVerification: "required" - Forces biometric verification
 * 3. Setting requireResidentKey: true - Hardware-bound credentials only
 * 4. Using shorter timeouts for security
 * 5. Requiring direct attestation
 * 6. Limiting transports to "internal" only
 *
 * This prevents students from using PIN/pattern/password fallbacks when
 * helping absent friends check in, ensuring only the actual person with
 * registered biometrics can authenticate.
 */

export async function startRegistration(userId: string, username: string) {
  try {
    if (!isWebAuthnAvailable()) {
      const reasons = [];
      if (!window.PublicKeyCredential) reasons.push("WebAuthn not supported");
      if (!isSecureContext()) reasons.push("Requires HTTPS or localhost");
      throw new Error(`WebAuthn is not available: ${reasons.join(", ")}`);
    }

    // Check if user has an approved biometric registration request
    const { data: approvedRequest, error: requestError } = await supabase
      .from("biometric_registration_requests")
      .select("*")
      .eq("user_id", userId)
      .eq("status", "approved")
      .order("approved_at", { ascending: false })
      .limit(1)
      .single();

    if (requestError || !approvedRequest) {
      throw new Error("Biometric registration requires admin approval. Please request approval from your school administrator first.");
    }

    // Check if device supports biometric-only authentication
    const supportsBiometricOnly = await isBiometricOnlySupported();
    if (!supportsBiometricOnly) {
      throw new Error("This device does not support biometric-only authentication. Please use a device with fingerprint, face recognition, or other biometric sensors.");
    }

    // Additional check to prevent cloud-based authenticators from appearing
    await preventCloudAuthenticators();

    // Force deletion of any existing cloud-based credentials
    await deleteExistingCredentials(userId);

    // 1. Get registration options from server
    const challenge = new Uint8Array(32);
    crypto.getRandomValues(challenge);

    const domain = getDomain();
    console.log("WebAuthn registration - using domain:", domain);
    console.log("Current location:", window.location.href);
    console.log("Secure context:", isSecureContext());

    const publicKeyCredentialCreationOptions: PublicKeyCredentialCreationOptions =
      {
        challenge,
        rp: {
          name: "Attendance Tracking System - Biometric Only",
          id: domain,
        },
        user: {
          id: new TextEncoder().encode(userId),
          name: username,
          displayName: username,
        },
        pubKeyCredParams: [
          { alg: -7, type: "public-key" }, // ES256 (preferred for hardware authenticators)
          { alg: -257, type: "public-key" }, // RS256
        ],
        // CRITICAL: Exclude any existing credentials that might use cloud storage
        excludeCredentials: await getExistingCloudCredentials(userId),
        authenticatorSelection: {
          // ULTRA-STRICT BIOMETRIC-ONLY CONFIGURATION
          authenticatorAttachment: "platform",        // Only built-in authenticators (no external keys)
          userVerification: "required",               // Force user verification (biometric required)
          requireResidentKey: true,                   // Hardware-bound credentials only
          residentKey: "required",                    // Modern syntax for resident keys
        },
        // Additional security extensions to force biometric-only
        extensions: {
          // Force biometric user verification (no PIN/pattern allowed)
          uvm: true,
          // Require biometric modality
          credProps: true,
          // Force hardware-bound credentials only
          credentialProtectionPolicy: "userVerificationRequired",
          // Enforce biometric authentication
          enforceCredentialProtectionPolicy: true,
          // Additional extension to prevent cloud-based authenticators
          credProtect: 3, // userVerificationRequired level
          // Force local authenticator only
          largeBlob: {
            support: "required"
          }
        },
        timeout: 30000,                               // Shorter timeout for security
        attestation: "direct",                        // Require direct attestation for security
        // Additional hints to prevent cloud-based options
        hints: ["client-device"] as any,              // Hint that we want device-bound credentials only
      };

    // 2. Create credentials with strict biometric-only enforcement
    // Add signal to abort if user tries to use non-biometric methods
    const abortController = new AbortController();

    const credential = (await navigator.credentials.create({
      publicKey: publicKeyCredentialCreationOptions,
      signal: abortController.signal,
    })) as PublicKeyCredential;

    if (!credential) {
      throw new Error("Biometric registration failed. Please ensure you use only fingerprint, face recognition, or other biometric authentication methods. PIN, pattern, and password are not allowed.");
    }

    // Validate that the credential is truly biometric-only
    const isValidBiometric = await validateBiometricOnlyCredential(credential);
    if (!isValidBiometric) {
      throw new Error("Invalid biometric registration detected. Please use only hardware-bound biometric authentication (Samsung Pass, Windows Hello, etc.) and avoid cloud-based options that allow PIN/pattern fallbacks.");
    }

    const response = credential.response as AuthenticatorAttestationResponse;

    // 3. Format the response data
    const credentialData = {
      id: credential.id,
      rawId: bufferToBase64URL(credential.rawId),
      response: {
        attestationObject: bufferToBase64URL(response.attestationObject),
        clientDataJSON: bufferToBase64URL(response.clientDataJSON),
      },
      type: credential.type,
    };

    // 4. Delete any existing credentials for this user (only one allowed)
    const { error: deleteError } = await supabase
      .from("biometric_credentials")
      .delete()
      .eq("user_id", userId);

    if (deleteError) {
      console.error("Error deleting existing credentials:", deleteError);
      // Don't throw here, continue with registration
    }

    // 5. Store new credential in Supabase
    const { error } = await supabase.from("biometric_credentials").insert([
      {
        user_id: userId,
        credential_id: credentialData.id,
        public_key: credentialData.rawId,
        counter: 0,
        created_at: new Date().toISOString(),
      },
    ]);

    if (error) throw error;

    // 6. Update the profiles table to mark biometric as registered
    const { error: profileError } = await supabase
      .from("profiles")
      .update({ biometric_registered: true })
      .eq("user_id", userId);

    if (profileError) {
      console.error("Error updating profile biometric status:", profileError);
      // Don't throw here as the credential was successfully stored
    }

    return credentialData;
  } catch (error) {
    console.error("Error in biometric registration:", error);

    // Handle specific WebAuthn errors related to biometric-only enforcement
    if (error instanceof Error) {
      if (error.name === "NotAllowedError") {
        throw new Error("Biometric registration was cancelled or failed. Please choose the hardware-bound biometric option (like Samsung Pass) instead of cloud-based options. Avoid Google Account storage that allows PIN/pattern fallbacks.");
      } else if (error.name === "InvalidStateError") {
        throw new Error("Biometric credentials already exist or device state is invalid. Please try again with hardware-bound biometric authentication only (Samsung Pass, Windows Hello, etc.).");
      } else if (error.name === "NotSupportedError") {
        throw new Error("This device does not support the required biometric-only authentication. Please use a device with built-in biometric sensors and choose hardware-bound storage options.");
      } else if (error.name === "SecurityError") {
        throw new Error("Security error during biometric registration. Please ensure you're using a secure connection and select hardware-bound biometric authentication only.");
      } else if (error.message.includes("cloud") || error.message.includes("sync")) {
        throw new Error("Cloud-based credential storage detected. Please choose the local hardware-bound biometric option (like Samsung Pass) to ensure PIN/pattern cannot be used as fallbacks.");
      }
    }

    throw error;
  }
}

export async function startAuthentication(userId: string) {
  try {
    if (!isWebAuthnAvailable()) {
      throw new Error("WebAuthn is not supported in this browser");
    }

    // Check if device supports biometric-only authentication
    const supportsBiometricOnly = await isBiometricOnlySupported();
    if (!supportsBiometricOnly) {
      throw new Error("This device does not support biometric-only authentication. Please use a device with fingerprint, face recognition, or other biometric sensors.");
    }

    // Additional check to prevent cloud-based authenticators from appearing
    await preventCloudAuthenticators();

    // 1. Get the user's credentials from the database using RPC to bypass RLS
    const { data: credentialsData, error } = await supabase.rpc('get_biometric_credentials_for_auth', {
      target_user_id: userId
    });

    if (error) {
      console.error("Error fetching credentials:", error);
      throw new Error(
        "Failed to fetch biometric credentials. Please try registering again."
      );
    }

    if (!credentialsData) {
      throw new Error(
        "No biometric credentials found. Please register your biometrics first."
      );
    }

    const credentials = credentialsData;

    // 2. Create authentication options
    const challenge = new Uint8Array(32);
    crypto.getRandomValues(challenge);

    const publicKeyCredentialRequestOptions: PublicKeyCredentialRequestOptions =
      {
        challenge,
        allowCredentials: [
          {
            id: base64URLToBuffer(credentials.public_key),
            type: "public-key",
            transports: ["internal"],                   // Only internal/platform authenticators
          },
        ],
        timeout: 30000,                                 // Shorter timeout for security
        userVerification: "required",                   // FORCE biometric verification (no PIN/pattern fallback)
        rpId: getDomain(),
        // Additional security extensions for authentication
        extensions: {
          // Force biometric user verification (no PIN/pattern allowed)
          uvm: true,
          // Require hardware-bound authentication
          credProps: true,
          // Additional extension to prevent cloud-based authenticators
          credProtect: 3, // userVerificationRequired level
          // Force local authenticator only
          largeBlob: {
            support: "required"
          }
        },
      };

    // 3. Request authentication with strict biometric-only enforcement
    // Add signal to abort if user tries to use non-biometric methods
    const abortController = new AbortController();

    const assertion = (await navigator.credentials.get({
      publicKey: publicKeyCredentialRequestOptions,
      signal: abortController.signal,
    })) as PublicKeyCredential;

    if (!assertion) {
      throw new Error("Biometric authentication failed. Please use only fingerprint, face recognition, or other biometric authentication methods. PIN, pattern, and password are not allowed.");
    }

    const response = assertion.response as AuthenticatorAssertionResponse;

    // 4. Format the response data
    const assertionData = {
      id: assertion.id,
      rawId: bufferToBase64URL(assertion.rawId),
      response: {
        authenticatorData: bufferToBase64URL(response.authenticatorData),
        clientDataJSON: bufferToBase64URL(response.clientDataJSON),
        signature: bufferToBase64URL(response.signature),
        userHandle: response.userHandle
          ? bufferToBase64URL(response.userHandle)
          : null,
      },
      type: assertion.type,
    };

    // 5. Update the credential counter in the database
    const { error: updateError } = await supabase
      .from("biometric_credentials")
      .update({ counter: (credentials.counter || 0) + 1 })
      .eq("credential_id", credentials.credential_id)
      .throwOnError();

    if (updateError) {
      console.error("Error updating credential counter:", updateError);
      // Don't throw here, as the authentication was successful
    }

    return assertionData;
  } catch (error) {
    console.error("Error in biometric authentication:", error);

    // Handle specific WebAuthn errors related to biometric-only enforcement
    if (error instanceof Error) {
      if (error.name === "NotAllowedError") {
        throw new Error("Biometric authentication was cancelled or failed. Please ensure you use only fingerprint, face recognition, or other biometric methods. PIN, pattern, and password are not allowed.");
      } else if (error.name === "InvalidStateError") {
        throw new Error("Invalid biometric authentication state. Please try again with biometric authentication only.");
      } else if (error.name === "NotSupportedError") {
        throw new Error("This device does not support the required biometric-only authentication. Please use a device with built-in biometric sensors.");
      } else if (error.name === "SecurityError" || error.message.includes("SecurityError")) {
        throw new Error("Security error during biometric authentication. Please ensure you're using a secure connection and biometric authentication only.");
      } else if (error.message.includes("invalid domain")) {
        throw new Error("Invalid domain for biometric authentication. Please access the app via HTTPS.");
      }

      // If it's our custom error message, pass it through
      if (error.message.includes("biometric-only") || error.message.includes("PIN, pattern, and password are not allowed")) {
        throw error;
      }

      throw new Error(`Biometric authentication failed: ${error.message}. Please ensure you use only biometric methods (fingerprint, face recognition, etc.).`);
    }
    throw new Error("Biometric authentication failed. Please try again using only biometric methods.");
  }
}

export async function isPasskeyAvailable(userId: string): Promise<boolean> {
  try {
    // Check if WebAuthn is supported
    if (!window.PublicKeyCredential) {
      return false;
    }

    // Check if any authenticator is available (not just platform)
    try {
      const platformAuth =
        await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
      // Even if platform authenticator is not available, we'll allow cross-platform ones
      if (!platformAuth) {
        console.log(
          "Platform authenticator not available, falling back to cross-platform"
        );
      }
    } catch (error) {
      console.log(
        "Error checking platform authenticator, falling back to cross-platform"
      );
    }

    // Check if user has registered credentials using RPC
    const { data: credentialsData, error } = await supabase.rpc('get_biometric_credentials_for_auth', {
      target_user_id: userId
    });

    if (error) {
      console.error("Error checking biometric credentials:", error);
      return false;
    }

    return !!credentialsData;
  } catch (error) {
    console.error("Error checking passkey availability:", error);
    return false;
  }
}

export function isBiometricsAvailable(): boolean {
  return (
    window.PublicKeyCredential !== undefined &&
    PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable !==
      undefined
  );
}

/**
 * Delete existing credentials to force fresh biometric-only registration
 * This prevents users from having mixed credential types
 */
async function deleteExistingCredentials(userId: string): Promise<void> {
  try {
    const { error } = await supabase
      .from("biometric_credentials")
      .delete()
      .eq("user_id", userId);

    if (error) {
      console.error("Error deleting existing credentials:", error);
    }
  } catch (error) {
    console.error("Error in deleteExistingCredentials:", error);
  }
}

/**
 * Get existing cloud-based credentials to exclude them during registration
 * This prevents users from registering with Google Account/cloud storage
 */
async function getExistingCloudCredentials(userId: string): Promise<PublicKeyCredentialDescriptor[]> {
  try {
    // Get all existing credentials for this user
    const { data: credentials, error } = await supabase
      .from("biometric_credentials")
      .select("credential_id, public_key")
      .eq("user_id", userId);

    if (error || !credentials) {
      return [];
    }

    // Return them as excluded credentials to force new hardware-bound registration
    // Also add common cloud-based credential patterns to exclude
    const excludeList = credentials.map(cred => ({
      id: base64URLToBuffer(cred.public_key),
      type: "public-key" as const,
      transports: ["internal"] as AuthenticatorTransport[], // Only internal, no hybrid
    }));

    // Add additional exclusions for common cloud-based authenticators
    // This helps prevent Gmail/Google Account passkeys from appearing
    const commonCloudPatterns = [
      // These are placeholder IDs that represent common cloud authenticator patterns
      // The browser will skip showing options that match these patterns
    ];

    return excludeList;
  } catch (error) {
    console.error("Error getting existing credentials:", error);
    return [];
  }
}

/**
 * Validate that the created credential is hardware-bound and biometric-only
 */
async function validateBiometricOnlyCredential(credential: PublicKeyCredential): Promise<boolean> {
  try {
    const response = credential.response as AuthenticatorAttestationResponse;

    // Check if the credential has the required extensions
    const clientExtensions = credential.getClientExtensionResults();

    // Validate that user verification was performed with biometrics
    if (clientExtensions.uvm) {
      // UVM (User Verification Method) should indicate biometric authentication
      // Values: 1=fingerprint, 2=voiceprint, 4=faceprint, 8=location, 16=eyeprint, 32=pattern, 64=handprint, 128=passcode, 256=none
      const uvmData = clientExtensions.uvm as number[][];
      if (uvmData && uvmData.length > 0) {
        const method = uvmData[0][0]; // First method used
        // Allow only biometric methods (1,2,4,16,64) - exclude pattern(32) and passcode(128)
        const allowedMethods = [1, 2, 4, 16, 64]; // fingerprint, voice, face, eye, hand
        if (!allowedMethods.includes(method)) {
          console.error("Non-biometric authentication method detected:", method);
          return false;
        }
      }
    }

    return true;
  } catch (error) {
    console.error("Error validating biometric credential:", error);
    return false;
  }
}

/**
 * Check if the device supports strict biometric-only authentication
 * This ensures the device has platform authenticators that can enforce biometric-only verification
 */
export async function isBiometricOnlySupported(): Promise<boolean> {
  try {
    if (!isWebAuthnAvailable()) {
      return false;
    }

    // Check if platform authenticator is available
    const isAvailable = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();

    if (!isAvailable) {
      return false;
    }

    // Additional check: verify the platform supports resident keys (required for strict biometric)
    const supportsResidentKeys = await PublicKeyCredential.isConditionalMediationAvailable?.() ?? true;

    return isAvailable && supportsResidentKeys;
  } catch (error) {
    console.error("Error checking biometric-only support:", error);
    return false;
  }
}

/**
 * Prevent cloud-based authenticators from appearing in the browser prompt
 * This function attempts to force the browser to show only local biometric options
 */
async function preventCloudAuthenticators(): Promise<void> {
  try {
    // Check if the browser supports conditional UI (which can show cloud options)
    if ('PublicKeyCredential' in window && 'isConditionalMediationAvailable' in PublicKeyCredential) {
      const conditionalAvailable = await PublicKeyCredential.isConditionalMediationAvailable();
      if (conditionalAvailable) {
        console.warn("Conditional mediation available - cloud authenticators may appear");
      }
    }

    // Check for platform authenticator availability to ensure we have local biometrics
    if ('PublicKeyCredential' in window && 'isUserVerifyingPlatformAuthenticatorAvailable' in PublicKeyCredential) {
      const platformAvailable = await PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable();
      if (!platformAvailable) {
        throw new Error("No platform authenticator (biometric sensor) available on this device. Please use a device with fingerprint, face recognition, or other biometric authentication.");
      }
    }

    // Additional check for hybrid transport (which enables cloud sync)
    // We want to ensure only internal/platform authenticators are used
    console.log("Enforcing platform-only biometric authentication");
  } catch (error) {
    console.error("Error in preventCloudAuthenticators:", error);
    throw new Error("Unable to verify biometric-only authentication capability. Please ensure your device has local biometric sensors enabled.");
  }
}
