import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON>rk<PERSON> } from 'lucide-react';
import { useTranslation } from 'react-i18next';

export default function TypingIndicator() {
  const { t } = useTranslation();
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="flex gap-2 md:gap-3"
    >
      {/* Bot avatar */}
      <motion.div
        animate={{ scale: [1, 1.1, 1] }}
        transition={{ duration: 2, repeat: Infinity }}
        className="w-7 h-7 md:w-8 md:h-8 rounded-full bg-gradient-to-br from-[#EE0D09] to-[#FF1A1A] flex items-center justify-center flex-shrink-0"
      >
        <Bot className="w-3 h-3 md:w-4 md:h-4 text-white" />
      </motion.div>

      {/* Typing bubble */}
      <div className="flex-1 max-w-[240px] md:max-w-[280px]">
        <motion.div
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          className="relative px-3 md:px-4 py-2 md:py-3 rounded-2xl rounded-bl-md bg-white/10 backdrop-blur-sm border border-white/20"
        >
          {/* Gradient border effect */}
          <div className="absolute inset-0 rounded-2xl rounded-bl-md bg-gradient-to-r from-[#EE0D09]/20 to-[#FF1A1A]/20 -z-10" />

          {/* Typing dots */}
          <div className="flex items-center gap-1">
            {[0, 1, 2].map((index) => (
              <motion.div
                key={index}
                animate={{
                  y: [0, -4, 0],
                  opacity: [0.4, 1, 0.4]
                }}
                transition={{
                  duration: 1.2,
                  repeat: Infinity,
                  delay: index * 0.15,
                  ease: "easeInOut"
                }}
                className="w-2 h-2 md:w-2.5 md:h-2.5 bg-gray-300 rounded-full"
              />
            ))}
          </div>

          {/* Sparkle indicator */}
          <motion.div
            animate={{ 
              rotate: [0, 360],
              scale: [1, 1.2, 1]
            }}
            transition={{ 
              duration: 3, 
              repeat: Infinity,
              ease: "linear"
            }}
            className="absolute -top-1 -right-1"
          >
            <Sparkles className="w-2 h-2 md:w-3 md:h-3 text-[#EE0D09]" />
          </motion.div>
        </motion.div>

        {/* Animated thinking text */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mt-1 text-xs text-gray-400"
        >
          <motion.span
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            {t('aiAssistant.typing')}
          </motion.span>
        </motion.div>
      </div>
    </motion.div>
  );
}
