/**
 * Application Configuration
 * Centralized configuration management for the entire application
 */

import { env, isDevelopment, isProduction } from './environment';
import { APP_CONFIG, USER_ROLES, ROUTES } from '../lib/constants';

export interface AppConfig {
  // Application Info
  name: string;
  shortName: string;
  description: string;
  version: string;
  
  // Environment
  environment: 'development' | 'production' | 'test';
  isDevelopment: boolean;
  isProduction: boolean;
  
  // Features
  features: {
    biometricAuth: boolean;
    qrCodeScanning: boolean;
    locationVerification: boolean;
    realTimeUpdates: boolean;
    offlineMode: boolean;
    analytics: boolean;
    errorReporting: boolean;
    debugMode: boolean;
  };
  
  // API Configuration
  api: {
    baseUrl: string;
    timeout: number;
    retryAttempts: number;
    retryDelay: number;
  };
  
  // UI Configuration
  ui: {
    theme: {
      defaultMode: 'light' | 'dark' | 'system';
      allowUserToggle: boolean;
    };
    animations: {
      enabled: boolean;
      duration: number;
    };
    notifications: {
      position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
      duration: number;
      maxVisible: number;
    };
  };
  
  // Security Configuration
  security: {
    sessionTimeout: number;
    maxLoginAttempts: number;
    passwordMinLength: number;
    requireBiometric: boolean;
    enableAuditLog: boolean;
  };
  
  // Performance Configuration
  performance: {
    cacheTimeout: number;
    maxConcurrentRequests: number;
    enableServiceWorker: boolean;
    enableLazyLoading: boolean;
  };
}

export const appConfig: AppConfig = {
  // Application Info
  name: env.APP_NAME,
  shortName: env.APP_SHORT_NAME,
  description: env.APP_DESCRIPTION,
  version: env.APP_VERSION,
  
  // Environment
  environment: env.NODE_ENV,
  isDevelopment,
  isProduction,
  
  // Features
  features: {
    biometricAuth: true,
    qrCodeScanning: true,
    locationVerification: true,
    realTimeUpdates: true,
    offlineMode: true,
    analytics: isProduction,
    errorReporting: isProduction,
    debugMode: env.DEV_MODE,
  },
  
  // API Configuration
  api: {
    baseUrl: env.API_BASE_URL,
    timeout: env.REQUEST_TIMEOUT,
    retryAttempts: isDevelopment ? 1 : 3,
    retryDelay: 1000,
  },
  
  // UI Configuration
  ui: {
    theme: {
      defaultMode: 'system',
      allowUserToggle: true,
    },
    animations: {
      enabled: true,
      duration: 200,
    },
    notifications: {
      position: 'top-right',
      duration: 5000,
      maxVisible: 5,
    },
  },
  
  // Security Configuration
  security: {
    sessionTimeout: 8 * 60 * 60 * 1000, // 8 hours
    maxLoginAttempts: 5,
    passwordMinLength: 8,
    requireBiometric: false,
    enableAuditLog: isProduction,
  },
  
  // Performance Configuration
  performance: {
    cacheTimeout: env.CACHE_TTL,
    maxConcurrentRequests: env.MAX_CONCURRENT_REQUESTS,
    enableServiceWorker: isProduction,
    enableLazyLoading: true,
  },
};

// Route configuration
export const routeConfig = {
  public: [ROUTES.HOME, ROUTES.LOGIN, ROUTES.SIGNUP, ROUTES.NOT_FOUND, ROUTES.OFFLINE],
  protected: [ROUTES.STUDENT, ROUTES.TEACHER, ROUTES.ADMIN, ROUTES.SYSTEM_ADMIN, ROUTES.PROFILE],
  roleBasedAccess: {
    [USER_ROLES.STUDENT]: [ROUTES.STUDENT, ROUTES.PROFILE],
    [USER_ROLES.TEACHER]: [ROUTES.TEACHER, ROUTES.PROFILE],
    [USER_ROLES.ADMIN]: [ROUTES.ADMIN, ROUTES.PROFILE],
    [USER_ROLES.SYSTEM_ADMIN]: [ROUTES.SYSTEM_ADMIN, ROUTES.PROFILE],
  },
};

// Default settings for new schools
export const defaultSchoolSettings = {
  attendance: {
    startTime: '08:00',
    endTime: '17:00',
    lateThresholdMinutes: 15,
    gracePeriodMinutes: 5,
    autoMarkAbsentAfterMinutes: 60,
  },
  excuses: {
    maxAdvanceDays: 7,
    maxDurationDays: 3,
    requireApproval: true,
  },
  notifications: {
    emailEnabled: true,
    smsEnabled: false,
    pushEnabled: true,
    parentNotifications: true,
  },
  security: {
    locationVerification: true,
    biometricRequired: false,
    qrCodeExpiry: 15,
  },
};

// Export environment validation
export { validateEnvironment } from './environment';
