-- Create biometric deletion requests system
-- This migration creates the complete biometric deletion approval workflow

-- Step 1: Create the biometric_deletion_requests table
CREATE TABLE IF NOT EXISTS public.biometric_deletion_requests (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    school_id UUID NOT NULL REFERENCES public.schools(id) ON DELETE CASCADE,
    student_name TEXT NOT NULL,
    student_email TEXT NOT NULL,
    student_number TEXT,
    block_name TEXT,
    room_name TEXT,
    request_reason TEXT,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    admin_notes TEXT,
    approved_by UUID REFERENCES auth.users(id),
    approved_at TIMESTAMPTZ,
    rejected_by UUID REFERENCES auth.users(id),
    rejected_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Step 2: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_biometric_deletion_requests_user_id ON public.biometric_deletion_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_biometric_deletion_requests_school_id ON public.biometric_deletion_requests(school_id);
CREATE INDEX IF NOT EXISTS idx_biometric_deletion_requests_status ON public.biometric_deletion_requests(status);
CREATE INDEX IF NOT EXISTS idx_biometric_deletion_requests_created_at ON public.biometric_deletion_requests(created_at);

-- Step 3: Enable RLS
ALTER TABLE public.biometric_deletion_requests ENABLE ROW LEVEL SECURITY;

-- Step 4: Create RLS policies

-- Students can view their own deletion requests
CREATE POLICY "Students can view their own deletion requests"
    ON public.biometric_deletion_requests
    FOR SELECT
    TO authenticated
    USING (auth.uid() = user_id);

-- Students can create their own deletion requests
CREATE POLICY "Students can create their own deletion requests"
    ON public.biometric_deletion_requests
    FOR INSERT
    TO authenticated
    WITH CHECK (auth.uid() = user_id);

-- School admins can view deletion requests for their school
CREATE POLICY "School admins can view deletion requests for their school"
    ON public.biometric_deletion_requests
    FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE profiles.user_id = auth.uid()
            AND profiles.role = 'admin'
            AND profiles.school_id = biometric_deletion_requests.school_id
        )
    );

-- School admins can update deletion requests for their school (for approval/rejection)
CREATE POLICY "School admins can update deletion requests for their school"
    ON public.biometric_deletion_requests
    FOR UPDATE
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE profiles.user_id = auth.uid()
            AND profiles.role = 'admin'
            AND profiles.school_id = biometric_deletion_requests.school_id
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE profiles.user_id = auth.uid()
            AND profiles.role = 'admin'
            AND profiles.school_id = biometric_deletion_requests.school_id
        )
    );

-- Step 5: Create updated_at trigger
CREATE TRIGGER update_biometric_deletion_requests_updated_at
    BEFORE UPDATE ON public.biometric_deletion_requests
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Step 6: Create RPC function to approve deletion requests
CREATE OR REPLACE FUNCTION public.approve_biometric_deletion_request(
    request_id UUID,
    admin_notes_param TEXT DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    request_user_id UUID;
    request_school_id UUID;
    admin_profile_record RECORD;
BEGIN
    -- Get the admin's profile to verify they're an admin and get their school
    SELECT * INTO admin_profile_record
    FROM public.profiles
    WHERE user_id = auth.uid() AND role = 'admin';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Only school admins can approve biometric deletion requests';
    END IF;

    -- Get the request details and verify it belongs to the admin's school
    SELECT user_id, school_id INTO request_user_id, request_school_id
    FROM public.biometric_deletion_requests
    WHERE id = request_id AND status = 'pending';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Deletion request not found or already processed';
    END IF;
    
    -- Verify the request belongs to the admin's school
    IF request_school_id != admin_profile_record.school_id THEN
        RAISE EXCEPTION 'You can only approve deletion requests for your school';
    END IF;

    -- Update the request status to approved
    UPDATE public.biometric_deletion_requests
    SET 
        status = 'approved',
        admin_notes = admin_notes_param,
        approved_by = auth.uid(),
        approved_at = NOW(),
        updated_at = NOW()
    WHERE id = request_id;

    -- Delete the user's biometric credentials
    DELETE FROM public.biometric_credentials
    WHERE user_id = request_user_id;

    -- Create a notification for the student
    INSERT INTO public.notifications (
        type,
        title,
        message,
        student_id,
        metadata
    ) VALUES (
        'system',
        'Biometric Deletion Approved',
        'Your biometric deletion request has been approved. Your biometric credentials have been permanently deleted.',
        (SELECT id FROM public.profiles WHERE user_id = request_user_id),
        jsonb_build_object(
            'request_id', request_id,
            'admin_notes', admin_notes_param
        )
    );
END;
$$;

-- Step 7: Create RPC function to reject deletion requests
CREATE OR REPLACE FUNCTION public.reject_biometric_deletion_request(
    request_id UUID,
    admin_notes_param TEXT DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    request_user_id UUID;
    request_school_id UUID;
    admin_profile_record RECORD;
BEGIN
    -- Get the admin's profile to verify they're an admin and get their school
    SELECT * INTO admin_profile_record
    FROM public.profiles
    WHERE user_id = auth.uid() AND role = 'admin';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Only school admins can reject biometric deletion requests';
    END IF;

    -- Get the request details and verify it belongs to the admin's school
    SELECT user_id, school_id INTO request_user_id, request_school_id
    FROM public.biometric_deletion_requests
    WHERE id = request_id AND status = 'pending';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Deletion request not found or already processed';
    END IF;
    
    -- Verify the request belongs to the admin's school
    IF request_school_id != admin_profile_record.school_id THEN
        RAISE EXCEPTION 'You can only reject deletion requests for your school';
    END IF;

    -- Update the request status to rejected
    UPDATE public.biometric_deletion_requests
    SET 
        status = 'rejected',
        admin_notes = admin_notes_param,
        rejected_by = auth.uid(),
        rejected_at = NOW(),
        updated_at = NOW()
    WHERE id = request_id;

    -- Create a notification for the student
    INSERT INTO public.notifications (
        type,
        title,
        message,
        student_id,
        metadata
    ) VALUES (
        'system',
        'Biometric Deletion Rejected',
        CASE 
            WHEN admin_notes_param IS NOT NULL AND admin_notes_param != '' THEN
                'Your biometric deletion request has been rejected. Reason: ' || admin_notes_param
            ELSE
                'Your biometric deletion request has been rejected.'
        END,
        (SELECT id FROM public.profiles WHERE user_id = request_user_id),
        jsonb_build_object(
            'request_id', request_id,
            'admin_notes', admin_notes_param
        )
    );
END;
$$;

-- Step 8: Grant execute permissions on the RPC functions
GRANT EXECUTE ON FUNCTION public.approve_biometric_deletion_request(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.reject_biometric_deletion_request(UUID, TEXT) TO authenticated;

-- Step 9: Grant table permissions
GRANT ALL ON public.biometric_deletion_requests TO authenticated;
