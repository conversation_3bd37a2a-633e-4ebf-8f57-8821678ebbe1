import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Fingerprint,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Building,
  Calendar,
  MessageSquare,
  Eye,
  Loader2,
  Trash2,
} from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";
import { supabase } from "@/lib/supabase";
import { formatDistanceToNow } from "date-fns";
import { enUS, tr } from "date-fns/locale";

interface BiometricDeletionRequest {
  id: string;
  user_id: string;
  student_name: string;
  student_email: string;
  student_number?: string;
  block_name?: string;
  room_name?: string;
  request_reason?: string;
  status: 'pending' | 'approved' | 'rejected';
  admin_notes?: string;
  approved_by?: string;
  approved_at?: string;
  rejected_by?: string;
  rejected_at?: string;
  created_at: string;
  updated_at: string;
}

export default function BiometricDeletionRequests() {
  const [requests, setRequests] = useState<BiometricDeletionRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<BiometricDeletionRequest | null>(null);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [adminNotes, setAdminNotes] = useState("");
  const [processing, setProcessing] = useState(false);

  const { user } = useAuth();
  const { toast } = useToast();
  const { t, i18n } = useTranslation();

  const fetchRequests = async () => {
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from("biometric_deletion_requests")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;

      setRequests(data || []);
    } catch (error) {
      console.error("Error fetching biometric deletion requests:", error);
      toast({
        title: t("biometricDeletionRequests.errorLoading"),
        description: t("biometricDeletionRequests.errorLoadingMessage"),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRequests();
  }, []);

  const handleApprove = async () => {
    if (!selectedRequest) return;

    try {
      setProcessing(true);

      const { error } = await supabase.rpc("approve_biometric_deletion_request", {
        request_id: selectedRequest.id,
        admin_notes_param: adminNotes || null,
      });

      if (error) throw error;

      toast({
        title: t("biometricDeletionRequests.requestApproved"),
        description: t("biometricDeletionRequests.credentialsDeleted"),
      });

      // Update local state
      setRequests(prev => 
        prev.map(req => 
          req.id === selectedRequest.id 
            ? { ...req, status: 'approved', admin_notes: adminNotes, approved_by: user?.id, approved_at: new Date().toISOString() }
            : req
        )
      );

      setShowApproveDialog(false);
      setSelectedRequest(null);
      setAdminNotes("");
    } catch (error) {
      console.error("Error approving request:", error);
      toast({
        title: t("biometricDeletionRequests.errorApproving"),
        description: t("biometricDeletionRequests.errorApprovingMessage"),
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  const handleReject = async () => {
    if (!selectedRequest) return;

    try {
      setProcessing(true);

      const { error } = await supabase.rpc("reject_biometric_deletion_request", {
        request_id: selectedRequest.id,
        admin_notes_param: adminNotes || null,
      });

      if (error) throw error;

      toast({
        title: t("biometricDeletionRequests.requestRejected"),
        description: t("biometricDeletionRequests.studentNotified"),
      });

      // Update local state
      setRequests(prev => 
        prev.map(req => 
          req.id === selectedRequest.id 
            ? { ...req, status: 'rejected', admin_notes: adminNotes, rejected_by: user?.id, rejected_at: new Date().toISOString() }
            : req
        )
      );

      setShowRejectDialog(false);
      setSelectedRequest(null);
      setAdminNotes("");
    } catch (error) {
      console.error("Error rejecting request:", error);
      toast({
        title: t("biometricDeletionRequests.errorRejecting"),
        description: t("biometricDeletionRequests.errorRejectingMessage"),
        variant: "destructive",
      });
    } finally {
      setProcessing(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="text-yellow-600 border-yellow-300">
            <Clock className="w-3 h-3 mr-1" />
            {t("biometricDeletionRequests.pending")}
          </Badge>
        );
      case "approved":
        return (
          <Badge variant="outline" className="text-green-600 border-green-300">
            <CheckCircle className="w-3 h-3 mr-1" />
            {t("biometricDeletionRequests.approved")}
          </Badge>
        );
      case "rejected":
        return (
          <Badge variant="outline" className="text-red-600 border-red-300">
            <XCircle className="w-3 h-3 mr-1" />
            {t("biometricDeletionRequests.rejected")}
          </Badge>
        );
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const locale = i18n.language === "tr" ? tr : enUS;
    return formatDistanceToNow(date, { addSuffix: true, locale });
  };

  const pendingRequests = requests.filter(req => req.status === "pending");
  const processedRequests = requests.filter(req => req.status !== "pending");

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <Trash2 className="h-5 w-5 text-red-600" />
        <div>
          <h2 className="text-lg font-semibold">{t("biometricDeletionRequests.title")}</h2>
          <p className="text-sm text-muted-foreground">
            {t("biometricDeletionRequests.description")}
          </p>
        </div>
      </div>

      {/* Pending Requests */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-yellow-600" />
            {t("biometricDeletionRequests.pendingRequests")} ({pendingRequests.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {pendingRequests.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">
                {t("biometricDeletionRequests.noPendingRequests")}
              </h3>
              <p className="text-muted-foreground">
                {t("biometricDeletionRequests.noPendingRequestsMessage")}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("biometricDeletionRequests.student")}</TableHead>
                    <TableHead>{t("biometricDeletionRequests.location")}</TableHead>
                    <TableHead>{t("biometricDeletionRequests.requestDate")}</TableHead>
                    <TableHead>{t("biometricDeletionRequests.actions")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pendingRequests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{request.student_name}</div>
                          <div className="text-sm text-muted-foreground">{request.student_email}</div>
                          {request.student_number && (
                            <div className="text-xs text-muted-foreground">
                              {request.student_number}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {request.block_name && request.room_name
                            ? `${request.block_name} - ${request.room_name}`
                            : request.block_name || request.room_name || "-"}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">{formatDate(request.created_at)}</div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedRequest(request);
                              setShowDetailsDialog(true);
                            }}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            {t("biometricDeletionRequests.view")}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-green-600 border-green-300 hover:bg-green-50"
                            onClick={() => {
                              setSelectedRequest(request);
                              setAdminNotes("");
                              setShowApproveDialog(true);
                            }}
                          >
                            <CheckCircle className="h-3 w-3 mr-1" />
                            {t("biometricDeletionRequests.approve")}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 border-red-300 hover:bg-red-50"
                            onClick={() => {
                              setSelectedRequest(request);
                              setAdminNotes("");
                              setShowRejectDialog(true);
                            }}
                          >
                            <XCircle className="h-3 w-3 mr-1" />
                            {t("biometricDeletionRequests.reject")}
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Processed Requests */}
      {processedRequests.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              {t("biometricDeletionRequests.processedRequests")} ({processedRequests.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t("biometricDeletionRequests.student")}</TableHead>
                    <TableHead>{t("biometricDeletionRequests.status")}</TableHead>
                    <TableHead>{t("biometricDeletionRequests.processedDate")}</TableHead>
                    <TableHead>{t("biometricDeletionRequests.actions")}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {processedRequests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{request.student_name}</div>
                          <div className="text-sm text-muted-foreground">{request.student_email}</div>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(request.status)}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {formatDate(request.approved_at || request.rejected_at || request.updated_at)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedRequest(request);
                            setShowDetailsDialog(true);
                          }}
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          {t("biometricDeletionRequests.view")}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Request Details Dialog */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-600" />
              {t("biometricDeletionRequests.requestDetails")}
            </DialogTitle>
            <DialogDescription>
              {t("biometricDeletionRequests.requestDetailsDescription")}
            </DialogDescription>
          </DialogHeader>

          {selectedRequest && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">{t("biometricDeletionRequests.studentName")}</span>
                  </div>
                  <p className="text-sm pl-6">{selectedRequest.student_name}</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">{t("biometricDeletionRequests.studentEmail")}</span>
                  </div>
                  <p className="text-sm pl-6">{selectedRequest.student_email}</p>
                </div>

                {selectedRequest.student_number && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-medium">{t("biometricDeletionRequests.studentNumber")}</span>
                    </div>
                    <p className="text-sm pl-6">{selectedRequest.student_number}</p>
                  </div>
                )}

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Building className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">{t("biometricDeletionRequests.location")}</span>
                  </div>
                  <p className="text-sm pl-6">
                    {selectedRequest.block_name && selectedRequest.room_name
                      ? `${selectedRequest.block_name} - ${selectedRequest.room_name}`
                      : selectedRequest.block_name || selectedRequest.room_name || "-"}
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">{t("biometricDeletionRequests.requestDate")}</span>
                  </div>
                  <p className="text-sm pl-6">{formatDate(selectedRequest.created_at)}</p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">{t("biometricDeletionRequests.status")}</span>
                  </div>
                  <div className="pl-6">{getStatusBadge(selectedRequest.status)}</div>
                </div>
              </div>

              {selectedRequest.request_reason && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">{t("biometricDeletionRequests.requestReason")}</span>
                  </div>
                  <p className="text-sm pl-6 bg-muted p-3 rounded-md">{selectedRequest.request_reason}</p>
                </div>
              )}

              {selectedRequest.admin_notes && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">{t("biometricDeletionRequests.adminNotes")}</span>
                  </div>
                  <p className="text-sm pl-6 bg-muted p-3 rounded-md">{selectedRequest.admin_notes}</p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Approve Request Dialog */}
      <AlertDialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              {t("biometricDeletionRequests.approveRequest")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("biometricDeletionRequests.approveRequestDescription")}
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="space-y-4">
            <Textarea
              placeholder={t("biometricDeletionRequests.adminNotesPlaceholder")}
              value={adminNotes}
              onChange={(e) => setAdminNotes(e.target.value)}
              rows={3}
            />
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel disabled={processing}>
              {t("common.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleApprove}
              disabled={processing}
              className="bg-green-600 hover:bg-green-700"
            >
              {processing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {t("biometricDeletionRequests.approving")}
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {t("biometricDeletionRequests.approve")}
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Reject Request Dialog */}
      <AlertDialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-600" />
              {t("biometricDeletionRequests.rejectRequest")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("biometricDeletionRequests.rejectRequestDescription")}
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="space-y-4">
            <Textarea
              placeholder={t("biometricDeletionRequests.rejectionReasonPlaceholder")}
              value={adminNotes}
              onChange={(e) => setAdminNotes(e.target.value)}
              rows={3}
            />
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel disabled={processing}>
              {t("common.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleReject}
              disabled={processing}
              className="bg-red-600 hover:bg-red-700"
            >
              {processing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {t("biometricDeletionRequests.rejecting")}
                </>
              ) : (
                <>
                  <XCircle className="h-4 w-4 mr-2" />
                  {t("biometricDeletionRequests.reject")}
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
