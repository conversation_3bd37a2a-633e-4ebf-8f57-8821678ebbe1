import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Bot, Sparkles, Zap, X } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ChatBubbleProps {
  onClick: () => void;
  isOpen: boolean;
  hasUnreadMessages?: boolean;
  isTyping?: boolean;
  showWelcomeMessage?: boolean;
  onWelcomeMessageDismiss?: () => void;
}

export default function ChatBubble({
  onClick,
  isOpen,
  hasUnreadMessages = false,
  isTyping = false,
  showWelcomeMessage = false,
  onWelcomeMessageDismiss
}: ChatBubbleProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [showPulse, setShowPulse] = useState(true);

  useEffect(() => {
    // Show pulse animation for first 10 seconds, then every 30 seconds
    const initialTimer = setTimeout(() => {
      setShowPulse(false);
    }, 10000);

    const intervalTimer = setInterval(() => {
      setShowPulse(true);
      setTimeout(() => setShowPulse(false), 3000);
    }, 30000);

    return () => {
      clearTimeout(initialTimer);
      clearInterval(intervalTimer);
    };
  }, []);

  return (
    <div className="fixed bottom-3 right-3 sm:bottom-4 sm:right-4 md:bottom-6 md:right-6 z-50">
      {/* Welcome Message for First-Time Users */}
      <AnimatePresence>
        {showWelcomeMessage && !isOpen && (
          <motion.div
            initial={{ opacity: 0, x: 20, y: 10, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, y: 0, scale: 1 }}
            exit={{ opacity: 0, x: 20, y: 10, scale: 0.8 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
            className="absolute bottom-full right-0 mb-3 w-56 xs:w-64 sm:w-72 md:w-80 max-w-[calc(100vw-2rem)]"
          >
            <div className="relative bg-gradient-to-br from-[#08194A] via-[#0A1B4D] to-[#0C1E50] text-white rounded-xl shadow-2xl border border-[#EE0D09]/30 p-4">
              {/* Sparkle animation background */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent animate-pulse rounded-xl"></div>

              {/* Content */}
              <div className="relative z-10">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-[#EE0D09] to-[#FF1A1A] flex items-center justify-center flex-shrink-0">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="text-sm font-semibold text-white">ATS Assistant</h4>
                      <Sparkles className="w-3 h-3 text-[#EE0D09]" />
                    </div>
                    <p className="text-xs text-gray-300 leading-relaxed">
                      👋 Hi! I'm your AI Assistant. I can help you with attendance, QR scanning, notifications, and answer any questions about the system!
                    </p>
                  </div>
                </div>
              </div>

              {/* Arrow pointing to chat bubble */}
              <div className="absolute bottom-0 right-6 transform translate-y-full">
                <div className="w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-[#0C1E50]"></div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      <AnimatePresence>
        {!isOpen && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
            className="relative"
          >
            {/* Pulse rings for attention */}
            <AnimatePresence>
              {(showPulse || hasUnreadMessages) && (
                <>
                  <motion.div
                    initial={{ scale: 1, opacity: 0.6 }}
                    animate={{ scale: 2, opacity: 0 }}
                    exit={{ scale: 1, opacity: 0 }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="absolute inset-0 rounded-full bg-gradient-to-r from-[#EE0D09] to-[#FF1A1A]"
                  />
                  <motion.div
                    initial={{ scale: 1, opacity: 0.4 }}
                    animate={{ scale: 1.5, opacity: 0 }}
                    exit={{ scale: 1, opacity: 0 }}
                    transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
                    className="absolute inset-0 rounded-full bg-gradient-to-r from-[#EE0D09] to-[#FF1A1A]"
                  />
                </>
              )}
            </AnimatePresence>

            {/* Main chat bubble */}
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              onHoverStart={() => setIsHovered(true)}
              onHoverEnd={() => setIsHovered(false)}
              className="relative"
            >
              <Button
                onClick={onClick}
                size="lg"
                className="w-16 h-16 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full bg-gradient-to-br from-[#08194A] via-[#0A1B4D] to-[#0C1E50] hover:from-[#0A1B4D] hover:to-[#0E2053] border-2 border-[#EE0D09]/30 shadow-2xl transition-all duration-300"
              >
                <div className="relative">
                  {/* Main icon */}
                  <AnimatePresence mode="wait">
                    {isTyping ? (
                      <motion.div
                        key="typing"
                        initial={{ scale: 0, rotate: -180 }}
                        animate={{ scale: 1, rotate: 0 }}
                        exit={{ scale: 0, rotate: 180 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Zap className="w-6 h-6 sm:w-5 sm:h-5 md:w-6 md:h-6 text-[#EE0D09]" />
                      </motion.div>
                    ) : (
                      <motion.div
                        key="bot"
                        initial={{ scale: 0, rotate: -180 }}
                        animate={{ scale: 1, rotate: 0 }}
                        exit={{ scale: 0, rotate: 180 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Bot className="w-6 h-6 sm:w-5 sm:h-5 md:w-6 md:h-6 text-white" />
                      </motion.div>
                    )}
                  </AnimatePresence>

                  {/* Sparkle effect on hover */}
                  <AnimatePresence>
                    {isHovered && (
                      <motion.div
                        initial={{ scale: 0, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0, opacity: 0 }}
                        className="absolute -top-1 -right-1"
                      >
                        <Sparkles className="w-4 h-4 text-[#EE0D09]" />
                      </motion.div>
                    )}
                  </AnimatePresence>

                  {/* Unread indicator */}
                  <AnimatePresence>
                    {hasUnreadMessages && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        exit={{ scale: 0 }}
                        className="absolute -top-2 -right-2 w-4 h-4 bg-[#EE0D09] rounded-full border-2 border-white"
                      >
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 1, repeat: Infinity }}
                          className="w-full h-full bg-[#EE0D09] rounded-full"
                        />
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </Button>

              {/* Floating tooltip */}
              <AnimatePresence>
                {isHovered && (
                  <motion.div
                    initial={{ opacity: 0, x: 20, scale: 0.8 }}
                    animate={{ opacity: 1, x: 0, scale: 1 }}
                    exit={{ opacity: 0, x: 20, scale: 0.8 }}
                    transition={{ duration: 0.2 }}
                    className="absolute right-full mr-2 md:mr-4 top-1/2 -translate-y-1/2 whitespace-nowrap hidden sm:block"
                  >
                    <div className="bg-gradient-to-r from-[#08194A] to-[#0C1E50] text-white px-3 md:px-4 py-2 rounded-lg shadow-lg border border-[#EE0D09]/20">
                      <div className="flex items-center gap-2">
                        <Sparkles className="w-3 h-3 md:w-4 md:h-4 text-[#EE0D09]" />
                        <span className="text-xs md:text-sm font-medium">
                          {isTyping ? 'AI Assistant is thinking...' : 'AI Assistant - Ask me anything!'}
                        </span>
                      </div>

                      {/* Tooltip arrow */}
                      <div className="absolute left-full top-1/2 -translate-y-1/2 border-l-6 md:border-l-8 border-l-[#08194A] border-y-3 md:border-y-4 border-y-transparent" />
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Floating particles effect */}
            <AnimatePresence>
              {isHovered && (
                <div className="absolute inset-0 pointer-events-none">
                  {[...Array(6)].map((_, i) => (
                    <motion.div
                      key={i}
                      initial={{ 
                        scale: 0, 
                        x: 32, 
                        y: 32,
                        opacity: 0 
                      }}
                      animate={{ 
                        scale: [0, 1, 0], 
                        x: 32 + (Math.random() - 0.5) * 100,
                        y: 32 + (Math.random() - 0.5) * 100,
                        opacity: [0, 1, 0]
                      }}
                      transition={{ 
                        duration: 2,
                        delay: i * 0.1,
                        repeat: Infinity,
                        repeatDelay: 1
                      }}
                      className="absolute w-1 h-1 bg-[#EE0D09] rounded-full"
                    />
                  ))}
                </div>
              )}
            </AnimatePresence>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Close button when chat is open */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ type: "spring", damping: 20, stiffness: 300 }}
          >
            <Button
              onClick={onClick}
              size="lg"
              variant="outline"
              className="w-16 h-16 sm:w-14 sm:h-14 md:w-16 md:h-16 rounded-full bg-white/10 backdrop-blur-sm border-2 border-white/20 hover:bg-white/20 transition-all duration-300"
            >
              <X className="w-6 h-6 sm:w-5 sm:h-5 md:w-6 md:h-6 text-white" />
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
