# 🚀 Vercel Deployment Guide - Campus Guardian Attendance System

## 📋 Pre-Deployment Checklist

### ✅ **Files Created for Deployment**
- `vercel.json` - Vercel configuration for SPA routing
- `public/_redirects` - Backup redirect configuration
- Environment variables configured

### 🔧 **Environment Variables Required**

Set these in your Vercel project settings:

```env
VITE_SUPABASE_URL=https://viyhomdfeppsqzkofhol.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key_here
```

## 🚀 **Deployment Steps**

### **Option 1: Deploy via Vercel Dashboard**

1. **Connect Repository**:
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository

2. **Configure Build Settings**:
   - **Framework Preset**: Vite
   - **Build Command**: `npm run build`
   - **Output Directory**: `dist`
   - **Install Command**: `npm install`

3. **Set Environment Variables**:
   - Go to Project Settings → Environment Variables
   - Add your Supabase URL and anon key
   - Make sure they're available for all environments

4. **Deploy**:
   - Click "Deploy"
   - Wait for build to complete

### **Option 2: Deploy via Vercel CLI**

```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Deploy
vercel

# Follow the prompts:
# - Set up and deploy? Y
# - Which scope? (select your account)
# - Link to existing project? N
# - Project name: campus-guardian-attendance
# - Directory: ./
# - Override settings? N
```

## 🔧 **Configuration Files Explained**

### **vercel.json**
```json
{
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```
This ensures all routes (like `/dashboard`, `/teacher`) are handled by React Router instead of returning 404.

### **public/_redirects**
```
/*    /index.html   200
```
Backup configuration for SPA routing.

## 🛠️ **Troubleshooting Common Issues**

### **Issue 1: 404 on Page Reload**
- ✅ **Fixed by**: `vercel.json` rewrites configuration
- **Cause**: Vercel tries to find physical files for routes like `/dashboard`
- **Solution**: All routes redirect to `index.html` for client-side routing

### **Issue 2: Environment Variables Not Working**
- **Check**: Vercel project settings → Environment Variables
- **Ensure**: Variables start with `VITE_` prefix
- **Redeploy**: After adding variables

### **Issue 3: Build Failures**
- **Check**: Build logs in Vercel dashboard
- **Common fixes**:
  - Ensure all dependencies are in `package.json`
  - Check for TypeScript errors
  - Verify import paths

### **Issue 4: Supabase Connection Issues**
- **Verify**: Environment variables are set correctly
- **Check**: Supabase project is active
- **Test**: Database connection in production

### **Issue 5: Service Worker 404 Errors**
- **Fixed**: Service worker registration disabled by default
- **Cause**: Vite builds don't automatically include service workers
- **Solution**: Service worker unregistered to prevent console errors
- **Note**: Can be re-enabled later if offline functionality is needed

## 📊 **Post-Deployment Verification**

### **Test These Features**:
1. **Landing Page**: Loads correctly
2. **Authentication**: Login/signup works
3. **Routing**: All pages accessible via direct URL
4. **Page Reload**: No 404 errors on any route
5. **Database**: Data loads and saves correctly
6. **Real-time**: Live updates work
7. **Mobile**: Responsive design works

### **Performance Checks**:
- **Lighthouse Score**: Aim for 90+ performance
- **Load Time**: Under 3 seconds
- **Bundle Size**: Check for unnecessary imports

## 🔒 **Security Considerations**

### **Environment Variables**:
- Never commit `.env` files
- Use Vercel's environment variable system
- Rotate keys regularly

### **Supabase Security**:
- RLS policies are active
- Anon key is properly configured
- Service key is never exposed to client

## 🚀 **Deployment Commands**

```bash
# Build locally to test
npm run build

# Preview build locally
npm run preview

# Deploy to Vercel
vercel --prod

# Check deployment status
vercel ls

# View deployment logs
vercel logs [deployment-url]
```

## 📱 **Custom Domain Setup** (Optional)

1. **Add Domain in Vercel**:
   - Project Settings → Domains
   - Add your custom domain

2. **Configure DNS**:
   - Add CNAME record pointing to Vercel
   - Or use Vercel nameservers

3. **SSL Certificate**:
   - Automatically provided by Vercel
   - Usually takes 5-10 minutes

## 🎯 **Success Indicators**

✅ **Deployment Successful When**:
- Build completes without errors
- All routes work on direct access
- Page reloads don't show 404
- Environment variables are loaded
- Database connections work
- Real-time features function
- Mobile experience is smooth

## 🆘 **Getting Help**

If you encounter issues:
1. Check Vercel build logs
2. Test locally with `npm run build && npm run preview`
3. Verify environment variables
4. Check Supabase dashboard for errors
5. Review browser console for client-side errors

---

**Your Campus Guardian Attendance System is now ready for production! 🎉**
